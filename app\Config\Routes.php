<?php

use CodeIgniter\Router\RouteCollection;

/**
 * @var RouteCollection $routes
 */
$routes->get('/', 'Home::index');
$routes->get('home', 'Home::index');
$routes->get('home/login', 'Home::login');
$routes->post('home/authenticate', 'Home::authenticate');
$routes->get('dashboard', 'DashboardController::index');

// Dakoii System Routes
$routes->get('dakoii', 'Dakoii::index');
$routes->get('dakoii/dashboard', 'Dakoii::dashboard');
$routes->post('dakoii/authenticate', 'Dakoii::authenticate');
$routes->get('dakoii/organizations', 'Dakoii::organizations');
$routes->get('dakoii/organizations/create', 'Dakoii::createOrganization');
$routes->get('dakoii/admins', 'Dakoii::admins');
$routes->get('dakoii/admins/create', 'Dakoii::createAdmin');
$routes->get('dakoii/settings', 'Dakoii::settings');
$routes->get('dakoii/reports', 'Dakoii::reports');

// Administrator Settings Routes
$routes->group('admin', function($routes) {
    // Main admin dashboard
    $routes->get('/', 'AdminController::index');

    // Users Management Routes
    $routes->get('users', 'AdminController::users');
    $routes->get('users/create', 'AdminController::createUser');
    $routes->post('users', 'AdminController::storeUser');
    $routes->get('users/(:num)/edit', 'AdminController::editUser/$1');
    $routes->put('users/(:num)', 'AdminController::updateUser/$1');
    $routes->delete('users/(:num)', 'AdminController::deleteUser/$1');

    // Structure Management Routes
    $routes->get('structures', 'AdminController::structures');
    $routes->get('structures/create', 'AdminController::createStructure');
    $routes->post('structures', 'AdminController::storeStructure');

    // Appointments Routes
    $routes->get('appointments', 'AdminController::appointments');
    $routes->get('appointments/create', 'AdminController::createAppointment');
    $routes->post('appointments', 'AdminController::storeAppointment');

    // Plans Management Routes
    $routes->get('plans', 'AdminController::plans');

    // Budget Book Management Routes
    $routes->get('budget-books', 'AdminController::budgetBooks');

    // Legislated Activities Routes
    $routes->get('legislated-activities', 'AdminController::legislatedActivities');
    $routes->get('legislation', 'AdminController::legislation');
    $routes->get('legislation/create', 'AdminController::createLegislation');
    $routes->post('legislation', 'AdminController::storeLegislation');
    $routes->get('legislation/(:num)/activities', 'AdminController::activities/$1');
    $routes->get('legislation/(:num)/activities/create', 'AdminController::createActivity/$1');
    $routes->post('legislation/(:num)/activities', 'AdminController::storeActivity/$1');
});
