<?= $this->extend('templates/admin_template') ?>

<?= $this->section('title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('page_title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
    .admin-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .admin-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(0,0,0,0.12);
    }

    .breadcrumb-item a {
        color: #800000;
        text-decoration: none;
    }

    .breadcrumb-item a:hover {
        color: #28a745;
    }

    .activity-card {
        border-left: 4px solid #28a745;
        transition: all 0.3s ease;
    }

    .activity-card:hover {
        border-left-color: #800000;
        transform: translateX(5px);
    }

    .activity-card.critical {
        border-left-color: #dc3545;
    }

    .activity-card.high {
        border-left-color: #ffc107;
    }

    .table th {
        background-color: #f8f9fa;
        border-top: none;
        color: #800000;
        font-weight: 600;
    }

    .btn-action {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
        margin: 0 0.125rem;
    }

    .priority-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }

    .frequency-badge {
        font-size: 0.7rem;
        padding: 0.2rem 0.4rem;
    }

    .legislation-info {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border-radius: 10px;
        padding: 1rem;
        margin-bottom: 1rem;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="fade-in">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <?php foreach ($breadcrumbs as $index => $breadcrumb): ?>
                        <?php if ($index === count($breadcrumbs) - 1): ?>
                            <li class="breadcrumb-item active" aria-current="page"><?= esc($breadcrumb['title']) ?></li>
                        <?php else: ?>
                            <li class="breadcrumb-item">
                                <a href="<?= esc($breadcrumb['url']) ?>"><?= esc($breadcrumb['title']) ?></a>
                            </li>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Legislation Information -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="legislation-info">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h4 class="fw-bold mb-1" style="color: #800000;">
                            <i class="fas fa-balance-scale me-2"></i><?= esc($legislation['name']) ?>
                        </h4>
                        <p class="text-muted mb-1"><?= esc($legislation['description']) ?></p>
                        <div class="d-flex gap-3">
                            <span class="badge bg-primary"><?= esc($legislation['reference_number']) ?></span>
                            <span class="badge bg-success"><?= esc($legislation['status']) ?></span>
                            <small class="text-muted">Enacted: <?= date('M d, Y', strtotime($legislation['date_enacted'])) ?></small>
                        </div>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <div class="btn-group">
                            <a href="<?= site_url('admin/legislation') ?>" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to Legislation
                            </a>
                            <a href="<?= site_url('admin/legislation/' . $legislation['id'] . '/activities/create') ?>" class="btn btn-success">
                                <i class="fas fa-plus me-2"></i>Add Activity
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Activities Overview -->
    <div class="row g-4 mb-4">
        <div class="col-lg-3 col-md-6">
            <div class="admin-card p-3 text-center">
                <i class="fas fa-tasks fs-1 mb-2 text-primary"></i>
                <h5 class="fw-bold"><?= count($activities) ?></h5>
                <small class="text-muted">Total Activities</small>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="admin-card p-3 text-center">
                <i class="fas fa-exclamation-triangle fs-1 mb-2 text-danger"></i>
                <h5 class="fw-bold">
                    <?= count(array_filter($activities, function($a) { return $a['priority'] === 'Critical'; })) ?>
                </h5>
                <small class="text-muted">Critical Priority</small>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="admin-card p-3 text-center">
                <i class="fas fa-calendar-alt fs-1 mb-2 text-warning"></i>
                <h5 class="fw-bold">
                    <?= count(array_filter($activities, function($a) { return $a['frequency'] === 'Annual'; })) ?>
                </h5>
                <small class="text-muted">Annual Activities</small>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="admin-card p-3 text-center">
                <i class="fas fa-users fs-1 mb-2 text-info"></i>
                <h5 class="fw-bold"><?= count($groups) ?></h5>
                <small class="text-muted">Linked Groups</small>
            </div>
        </div>
    </div>



    <!-- Activities Table View -->
    <div class="row">
        <div class="col-12">
            <div class="admin-card p-4">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="fw-bold mb-0" style="color: #800000;">
                        <i class="fas fa-list me-2"></i>Activities List
                    </h5>
                    <div class="d-flex gap-2">
                        <select class="form-select form-select-sm" style="width: auto;">
                            <option>All Priorities</option>
                            <option>Critical</option>
                            <option>High</option>
                            <option>Medium</option>
                        </select>
                        <input type="text" class="form-control form-control-sm" placeholder="Search activities..." style="width: 200px;">
                        <button class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Activity Name</th>
                                <th>Priority</th>
                                <th>Frequency</th>
                                <th>Due Period</th>
                                <th>Linked Groups</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($activities as $activity): ?>
                            <tr>
                                <td>
                                    <div>
                                        <strong><?= esc($activity['name']) ?></strong>
                                        <br><small class="text-muted"><?= esc(substr($activity['description'], 0, 60)) ?>...</small>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-<?= $activity['priority'] === 'Critical' ? 'danger' : ($activity['priority'] === 'High' ? 'warning' : 'info') ?> priority-badge">
                                        <?= esc($activity['priority']) ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-secondary frequency-badge"><?= esc($activity['frequency']) ?></span>
                                </td>
                                <td><?= esc($activity['due_period']) ?></td>
                                <td>
                                    <small class="text-muted"><?= esc($activity['linked_groups']) ?></small>
                                </td>
                                <td>
                                    <span class="badge bg-success"><?= esc($activity['status']) ?></span>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button type="button"
                                                class="btn btn-outline-primary btn-action"
                                                title="Edit Activity">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button type="button"
                                                class="btn btn-outline-info btn-action"
                                                title="Manage Groups">
                                            <i class="fas fa-link"></i>
                                        </button>
                                        <button type="button"
                                                class="btn btn-outline-danger btn-action"
                                                title="Delete Activity">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Search functionality
        document.querySelector('input[placeholder="Search activities..."]').addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const tableRows = document.querySelectorAll('tbody tr');

            tableRows.forEach(row => {
                const text = row.textContent.toLowerCase();
                row.style.display = text.includes(searchTerm) ? '' : 'none';
            });
        });

        // Priority filter
        document.querySelector('select').addEventListener('change', function() {
            const filterValue = this.value;
            const tableRows = document.querySelectorAll('tbody tr');

            tableRows.forEach(row => {
                if (filterValue === 'All Priorities') {
                    row.style.display = '';
                } else {
                    const priorityCell = row.querySelector('.priority-badge');
                    const priority = priorityCell ? priorityCell.textContent.trim() : '';
                    row.style.display = priority === filterValue ? '' : 'none';
                }
            });
        });

        console.log('Activities Management initialized successfully');
    });
</script>
<?= $this->endSection() ?>
