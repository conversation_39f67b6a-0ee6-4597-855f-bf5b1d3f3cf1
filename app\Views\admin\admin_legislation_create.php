<?= $this->extend('templates/admin_template') ?>

<?= $this->section('title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('page_title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
    .admin-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .breadcrumb-item a {
        color: #800000;
        text-decoration: none;
    }

    .breadcrumb-item a:hover {
        color: #28a745;
    }

    .form-label {
        font-weight: 600;
        color: #495057;
    }

    .required {
        color: #dc3545;
    }

    .form-section {
        border-left: 4px solid #28a745;
        padding-left: 1rem;
        margin-bottom: 2rem;
    }

    .help-text {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1rem;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="fade-in">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <?php foreach ($breadcrumbs as $index => $breadcrumb): ?>
                        <?php if ($index === count($breadcrumbs) - 1): ?>
                            <li class="breadcrumb-item active" aria-current="page"><?= esc($breadcrumb['title']) ?></li>
                        <?php else: ?>
                            <li class="breadcrumb-item">
                                <a href="<?= esc($breadcrumb['url']) ?>"><?= esc($breadcrumb['title']) ?></a>
                            </li>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="admin-card p-4">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h3 class="mb-2 text-success">
                            <i class="fas fa-balance-scale me-2"></i>Create New Legislation
                        </h3>
                        <p class="text-muted mb-0">
                            Add new legislation that will define mandatory activities for the organization.
                        </p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <a href="<?= site_url('admin/legislation') ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Back to Legislation
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Help Information -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="help-text">
                <h6 class="fw-bold text-primary mb-2">
                    <i class="fas fa-info-circle me-2"></i>About Legislated Activities
                </h6>
                <p class="mb-2">
                    Legislation defines mandatory activities that organizations must perform. These activities will be:
                </p>
                <ul class="mb-0">
                    <li>Pre-listed by administrators and linked to specific organizational groups</li>
                    <li>Available for selection when creating workplans</li>
                    <li>Mandatory compliance requirements that must be included in annual planning</li>
                    <li>Tracked for completion and reporting purposes</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Create Legislation Form -->
    <form action="<?= site_url('admin/legislation') ?>" method="POST" id="createLegislationForm">
        <?= csrf_field() ?>
        
        <div class="row">
            <div class="col-12">
                <div class="admin-card p-4">
                    <!-- Basic Information Section -->
                    <div class="form-section">
                        <h5 class="fw-bold mb-3" style="color: #800000;">
                            <i class="fas fa-info-circle me-2"></i>Basic Information
                        </h5>
                        
                        <div class="row">
                            <div class="col-md-8 mb-3">
                                <label for="name" class="form-label">
                                    Legislation Name <span class="required">*</span>
                                </label>
                                <input type="text" class="form-control" id="name" name="name" required
                                       placeholder="e.g., Public Service Management Act 2014">
                                <div class="form-text">Enter the full official name of the legislation</div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="reference_number" class="form-label">
                                    Reference Number <span class="required">*</span>
                                </label>
                                <input type="text" class="form-control" id="reference_number" name="reference_number" required
                                       placeholder="e.g., PSM-2014-001">
                                <div class="form-text">Unique reference identifier</div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">
                                Description <span class="required">*</span>
                            </label>
                            <textarea class="form-control" id="description" name="description" rows="3" required
                                      placeholder="Brief description of the legislation and its purpose"></textarea>
                            <div class="form-text">Provide a clear description of what this legislation covers</div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="date_enacted" class="form-label">
                                    Date Enacted <span class="required">*</span>
                                </label>
                                <input type="date" class="form-control" id="date_enacted" name="date_enacted" required>
                                <div class="form-text">When this legislation was officially enacted</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="status" class="form-label">
                                    Status <span class="required">*</span>
                                </label>
                                <select class="form-select" id="status" name="status" required>
                                    <option value="">Select Status</option>
                                    <option value="Active" selected>Active</option>
                                    <option value="Inactive">Inactive</option>
                                    <option value="Under Review">Under Review</option>
                                    <option value="Superseded">Superseded</option>
                                </select>
                                <div class="form-text">Current status of this legislation</div>
                            </div>
                        </div>
                    </div>

                    <!-- Additional Details Section -->
                    <div class="form-section">
                        <h5 class="fw-bold mb-3" style="color: #800000;">
                            <i class="fas fa-file-alt me-2"></i>Additional Details
                        </h5>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="ministry" class="form-label">Responsible Ministry</label>
                                <input type="text" class="form-control" id="ministry" name="ministry"
                                       placeholder="e.g., Department of Personnel Management">
                                <div class="form-text">Ministry or department responsible for this legislation</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="category" class="form-label">Category</label>
                                <select class="form-select" id="category" name="category">
                                    <option value="">Select Category</option>
                                    <option value="Public Service">Public Service</option>
                                    <option value="Financial Management">Financial Management</option>
                                    <option value="Audit & Compliance">Audit & Compliance</option>
                                    <option value="Information Technology">Information Technology</option>
                                    <option value="Human Resources">Human Resources</option>
                                    <option value="Other">Other</option>
                                </select>
                                <div class="form-text">Category or type of legislation</div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="key_provisions" class="form-label">Key Provisions</label>
                            <textarea class="form-control" id="key_provisions" name="key_provisions" rows="4"
                                      placeholder="List the main provisions or requirements of this legislation"></textarea>
                            <div class="form-text">Summarize the main requirements or provisions</div>
                        </div>

                        <div class="mb-3">
                            <label for="compliance_notes" class="form-label">Compliance Notes</label>
                            <textarea class="form-control" id="compliance_notes" name="compliance_notes" rows="3"
                                      placeholder="Any specific compliance requirements or notes"></textarea>
                            <div class="form-text">Additional notes about compliance requirements</div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="d-flex gap-2 pt-3 border-top">
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save me-2"></i>Create Legislation
                        </button>
                        <a href="<?= site_url('admin/legislation') ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                        <button type="button" class="btn btn-outline-info ms-auto" onclick="previewForm()">
                            <i class="fas fa-eye me-2"></i>Preview
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </form>

    <!-- Next Steps Information -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="admin-card p-4">
                <h5 class="fw-bold mb-3" style="color: #800000;">
                    <i class="fas fa-arrow-right me-2"></i>Next Steps
                </h5>
                <p class="text-muted mb-3">After creating this legislation, you will be able to:</p>
                <div class="row">
                    <div class="col-md-6">
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <i class="fas fa-plus text-success me-2"></i>
                                Add mandatory activities under this legislation
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-link text-info me-2"></i>
                                Link activities to specific organizational groups
                            </li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <i class="fas fa-calendar text-warning me-2"></i>
                                Set frequency and due periods for activities
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-chart-bar text-primary me-2"></i>
                                Monitor compliance and completion rates
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Form validation
        document.getElementById('createLegislationForm').addEventListener('submit', function(e) {
            const name = document.getElementById('name').value.trim();
            const referenceNumber = document.getElementById('reference_number').value.trim();
            const description = document.getElementById('description').value.trim();
            const dateEnacted = document.getElementById('date_enacted').value;
            const status = document.getElementById('status').value;

            if (!name || !referenceNumber || !description || !dateEnacted || !status) {
                e.preventDefault();
                alert('Please fill in all required fields.');
                return false;
            }

            // Validate date is not in the future
            const enactedDate = new Date(dateEnacted);
            const today = new Date();
            if (enactedDate > today) {
                e.preventDefault();
                alert('Date enacted cannot be in the future.');
                return false;
            }
        });

        // Auto-generate reference number based on name
        document.getElementById('name').addEventListener('blur', function() {
            const name = this.value.trim();
            const referenceField = document.getElementById('reference_number');
            
            if (name && !referenceField.value) {
                // Simple auto-generation logic
                const words = name.split(' ');
                const acronym = words.map(word => word.charAt(0).toUpperCase()).join('');
                const year = new Date().getFullYear();
                const suggested = `${acronym}-${year}-001`;
                referenceField.value = suggested;
            }
        });

        console.log('Create Legislation form initialized successfully');
    });

    function previewForm() {
        const formData = new FormData(document.getElementById('createLegislationForm'));
        let preview = 'Legislation Preview:\n\n';
        
        for (let [key, value] of formData.entries()) {
            if (value.trim()) {
                preview += `${key.replace('_', ' ').toUpperCase()}: ${value}\n`;
            }
        }
        
        alert(preview);
    }
</script>
<?= $this->endSection() ?>
