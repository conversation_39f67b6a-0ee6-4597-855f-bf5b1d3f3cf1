<?= $this->extend('templates/admin_template') ?>

<?= $this->section('title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('page_title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
    .admin-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .breadcrumb-item a {
        color: #800000;
        text-decoration: none;
    }

    .breadcrumb-item a:hover {
        color: #28a745;
    }

    .table th {
        background-color: #f8f9fa;
        border-top: none;
        color: #800000;
        font-weight: 600;
    }

    .btn-action {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
        margin: 0 0.125rem;
    }

    .position-type-badge {
        font-size: 0.7rem;
        padding: 0.2rem 0.4rem;
    }

    .role-badge {
        font-size: 0.65rem;
        padding: 0.15rem 0.3rem;
        margin: 0.1rem;
    }

    .position-card {
        border: 1px solid #e9ecef;
        border-radius: 8px;
        transition: all 0.3s ease;
    }

    .position-card:hover {
        border-color: #800000;
        box-shadow: 0 2px 8px rgba(128, 0, 0, 0.1);
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="fade-in">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <?php foreach ($breadcrumbs as $index => $breadcrumb): ?>
                        <?php if ($index === count($breadcrumbs) - 1): ?>
                            <li class="breadcrumb-item active" aria-current="page"><?= esc($breadcrumb['title']) ?></li>
                        <?php else: ?>
                            <li class="breadcrumb-item">
                                <a href="<?= esc($breadcrumb['url']) ?>"><?= esc($breadcrumb['title']) ?></a>
                            </li>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="admin-card p-4">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h3 class="mb-2 text-success">
                            <i class="fas fa-user-tie me-2"></i>Positions Management
                        </h3>
                        <p class="text-muted mb-1">
                            Structure: <strong><?= esc($structure['name']) ?></strong>
                        </p>
                        <p class="text-muted mb-0">
                            Group: <strong><?= esc($group['name']) ?></strong>
                        </p>
                        <p class="text-muted mb-0 small">
                            Manage positions and roles within this group.
                        </p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <div class="btn-group">
                            <a href="<?= site_url('admin/structures/' . $group['structure_id'] . '/groups') ?>" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to Groups
                            </a>
                            <a href="<?= site_url('admin/groups/' . $group['id'] . '/positions/create') ?>" class="btn btn-success">
                                <i class="fas fa-plus me-2"></i>Create Position
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <?php if (session()->getFlashdata('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?= session()->getFlashdata('success') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Positions List -->
    <div class="row">
        <div class="col-12">
            <div class="admin-card p-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h5 class="fw-bold mb-0" style="color: #800000;">
                        <i class="fas fa-users-cog me-2"></i>Positions in <?= esc($group['name']) ?>
                    </h5>
                    <span class="badge bg-info">
                        <?= count($positions) ?> Position<?= count($positions) !== 1 ? 's' : '' ?>
                    </span>
                </div>

                <?php if (empty($positions)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-user-tie fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No Positions Found</h5>
                        <p class="text-muted">Create your first position within this group to get started.</p>
                        <a href="<?= site_url('admin/groups/' . $group['id'] . '/positions/create') ?>" class="btn btn-success">
                            <i class="fas fa-plus me-2"></i>Create First Position
                        </a>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Position No.</th>
                                    <th>Position Name</th>
                                    <th>Grade</th>
                                    <th>Type</th>
                                    <th>Roles & Permissions</th>
                                    <th>Reporting To</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($positions as $position): ?>
                                <tr>
                                    <td>
                                        <span class="badge bg-primary position-type-badge">
                                            <?= esc($position['position_no']) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <strong><?= esc($position['position_name']) ?></strong>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary position-type-badge">
                                            <?= esc($position['grade']) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?= $position['position_type'] === 'Public Servant' ? 'info' : 'warning' ?> position-type-badge">
                                            <?= esc($position['position_type']) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="d-flex flex-wrap">
                                            <?php if ($position['is_fund_manager']): ?>
                                                <span class="badge bg-success role-badge">Fund Manager</span>
                                            <?php endif; ?>
                                            <?php if ($position['is_supervisor']): ?>
                                                <span class="badge bg-warning role-badge">Supervisor</span>
                                            <?php endif; ?>
                                            <?php if ($position['is_group_admin']): ?>
                                                <span class="badge bg-danger role-badge">Group Admin</span>
                                            <?php endif; ?>
                                            <?php if (!$position['is_fund_manager'] && !$position['is_supervisor'] && !$position['is_group_admin']): ?>
                                                <span class="badge bg-light text-dark role-badge">Standard User</span>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <?php if ($position['reporting_to_id']): ?>
                                            <?php
                                            $reportingTo = array_filter($positions, function($p) use ($position) {
                                                return $p['id'] == $position['reporting_to_id'];
                                            });
                                            $reportingTo = reset($reportingTo);
                                            ?>
                                            <?php if ($reportingTo): ?>
                                                <small class="text-muted">
                                                    <?= esc($reportingTo['position_name']) ?>
                                                    <br>
                                                    <span class="badge bg-light text-dark" style="font-size: 0.6rem;">
                                                        <?= esc($reportingTo['position_no']) ?>
                                                    </span>
                                                </small>
                                            <?php else: ?>
                                                <small class="text-muted">External Position</small>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <span class="text-muted">None</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="<?= site_url('admin/groups/' . $group['id'] . '/positions/' . $position['id'] . '/edit') ?>" 
                                               class="btn btn-outline-primary btn-action" title="Edit Position">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <form method="POST" action="<?= site_url('admin/groups/' . $group['id'] . '/positions/' . $position['id']) ?>" class="d-inline">
                                                <?= csrf_field() ?>
                                                <input type="hidden" name="_method" value="DELETE">
                                                <button type="submit" class="btn btn-outline-danger btn-action" 
                                                        title="Delete Position"
                                                        onclick="return confirm('Are you sure you want to delete this position? This action cannot be undone.')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Navigation Help -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="admin-card p-3">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h6 class="mb-1 text-primary">
                            <i class="fas fa-info-circle me-2"></i>Navigation Flow
                        </h6>
                        <p class="mb-0 text-muted small">
                            Structure → Groups → <strong>Positions</strong>. You've reached the final level of the organizational hierarchy.
                        </p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <div class="btn-group">
                            <a href="<?= site_url('admin/structures/' . $group['structure_id'] . '/groups') ?>" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-arrow-left me-2"></i>Back to Groups
                            </a>
                            <a href="<?= site_url('admin/groups/' . $group['id'] . '/positions/create') ?>" class="btn btn-success btn-sm">
                                <i class="fas fa-plus me-2"></i>Add Position
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-hide alerts after 5 seconds
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(function(alert) {
            setTimeout(function() {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }, 5000);
        });

        console.log('Positions Management initialized successfully');
    });
</script>
<?= $this->endSection() ?>
