<?= $this->extend('templates/admin_template') ?>

<?= $this->section('title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('page_title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
    .admin-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .admin-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(0,0,0,0.12);
    }

    .breadcrumb-item a {
        color: #800000;
        text-decoration: none;
    }

    .breadcrumb-item a:hover {
        color: #28a745;
    }

    .table th {
        background-color: #f8f9fa;
        border-top: none;
        color: #800000;
        font-weight: 600;
    }

    .btn-action {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
        margin: 0 0.125rem;
    }

    .status-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }

    .plan-type-badge {
        font-size: 0.7rem;
        padding: 0.2rem 0.4rem;
    }

    .stats-card {
        background: linear-gradient(135deg, #800000 0%, #a00000 100%);
        border-radius: 10px;
        color: white;
        transition: all 0.3s ease;
    }

    .stats-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(128, 0, 0, 0.3);
    }

    .stats-card .stats-number {
        font-size: 2rem;
        font-weight: bold;
        color: #28a745;
    }

    .plan-card {
        border: 1px solid #e9ecef;
        border-radius: 10px;
        transition: all 0.3s ease;
        background: white;
    }

    .plan-card:hover {
        border-color: #800000;
        box-shadow: 0 4px 12px rgba(128, 0, 0, 0.1);
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="fade-in">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <?php foreach ($breadcrumbs as $index => $breadcrumb): ?>
                        <?php if ($index === count($breadcrumbs) - 1): ?>
                            <li class="breadcrumb-item active" aria-current="page"><?= esc($breadcrumb['title']) ?></li>
                        <?php else: ?>
                            <li class="breadcrumb-item">
                                <a href="<?= esc($breadcrumb['url']) ?>"><?= esc($breadcrumb['title']) ?></a>
                            </li>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="admin-card p-4">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h3 class="mb-2 text-success">
                            <i class="fas fa-clipboard-list me-2"></i>Plans Management
                        </h3>
                        <p class="text-muted mb-0">
                            Manage development plans, corporate plans, and their associated programs, projects, KRAs, and KPIs.
                        </p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <div class="btn-group">
                            <a href="<?= site_url('admin') ?>" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to Admin
                            </a>
                            <a href="<?= site_url('admin/plans/create') ?>" class="btn btn-success">
                                <i class="fas fa-plus me-2"></i>Create Plan
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stats-card p-3 text-center">
                <div class="stats-number"><?= $stats['total_plans'] ?></div>
                <div class="small">Total Plans</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card p-3 text-center">
                <div class="stats-number"><?= $stats['development_plans'] ?></div>
                <div class="small">Development Plans</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card p-3 text-center">
                <div class="stats-number"><?= $stats['corporate_plans'] ?></div>
                <div class="small">Corporate Plans</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card p-3 text-center">
                <div class="stats-number"><?= $stats['active_plans'] ?></div>
                <div class="small">Active Plans</div>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <?php if (session()->getFlashdata('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?= session()->getFlashdata('success') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Plans List -->
    <div class="row">
        <div class="col-12">
            <div class="admin-card p-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h5 class="fw-bold mb-0" style="color: #800000;">
                        <i class="fas fa-list me-2"></i>Strategic Plans
                    </h5>
                    <span class="badge bg-info">
                        <?= count($plans) ?> Plan<?= count($plans) !== 1 ? 's' : '' ?>
                    </span>
                </div>

                <?php if (empty($plans)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No Plans Found</h5>
                        <p class="text-muted">Create your first strategic plan to get started.</p>
                        <a href="<?= site_url('admin/plans/create') ?>" class="btn btn-success">
                            <i class="fas fa-plus me-2"></i>Create First Plan
                        </a>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Plan Name</th>
                                    <th>Type</th>
                                    <th>Fiscal Year</th>
                                    <th>Status</th>
                                    <th>Components</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($plans as $plan): ?>
                                <tr>
                                    <td>
                                        <div>
                                            <strong><?= esc($plan['name']) ?></strong>
                                            <br>
                                            <small class="text-muted"><?= esc($plan['description']) ?></small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?= $plan['type'] === 'Development' ? 'primary' : 'warning' ?> plan-type-badge">
                                            <?= esc($plan['type']) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary"><?= $plan['fiscal_year'] ?></span>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?= $plan['status'] === 'Active' ? 'success' : 'secondary' ?> status-badge">
                                            <?= esc($plan['status']) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php if ($plan['type'] === 'Development'): ?>
                                            <a href="<?= site_url('admin/plans/' . $plan['id'] . '/programs') ?>"
                                               class="btn btn-outline-info btn-sm">
                                                <i class="fas fa-project-diagram me-1"></i>Programs
                                            </a>
                                        <?php else: ?>
                                            <a href="<?= site_url('admin/plans/' . $plan['id'] . '/kras') ?>"
                                               class="btn btn-outline-warning btn-sm">
                                                <i class="fas fa-bullseye me-1"></i>KRAs
                                            </a>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            <?= date('M d, Y', strtotime($plan['created_at'])) ?>
                                        </small>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="<?= site_url('admin/plans/' . $plan['id'] . '/edit') ?>"
                                               class="btn btn-outline-primary btn-action" title="Edit Plan">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <form method="POST" action="<?= site_url('admin/plans/' . $plan['id']) ?>" class="d-inline">
                                                <?= csrf_field() ?>
                                                <input type="hidden" name="_method" value="DELETE">
                                                <button type="submit" class="btn btn-outline-danger btn-action"
                                                        title="Delete Plan"
                                                        onclick="return confirm('Are you sure you want to delete this plan? This will also delete all associated components.')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Navigation Help -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="admin-card p-3">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h6 class="mb-1 text-primary">
                            <i class="fas fa-info-circle me-2"></i>Navigation Flow
                        </h6>
                        <p class="mb-0 text-muted small">
                            <strong>Development Plans:</strong> Plans → Programs → Projects |
                            <strong>Corporate Plans:</strong> Plans → KRAs → KPIs
                        </p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <a href="<?= site_url('admin') ?>" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-arrow-left me-2"></i>Back to Admin Settings
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-hide alerts after 5 seconds
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(function(alert) {
            setTimeout(function() {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }, 5000);
        });

        console.log('Plans Management initialized successfully');
    });
</script>
<?= $this->endSection() ?>
