<?php

namespace App\Controllers;

class AdminPlansController extends BaseController
{
    /**
     * Display plans list (GET request)
     *
     * @return string
     */
    public function index()
    {
        $data = [
            'page_title' => 'Plans Management',
            'breadcrumbs' => [
                ['title' => 'Dashboard', 'url' => site_url('dashboard')],
                ['title' => 'Administrator Settings', 'url' => site_url('admin')],
                ['title' => 'Plans Management', 'url' => '']
            ],
            'plans' => $this->getDummyPlans(),
            'stats' => $this->getPlansStats()
        ];

        return view('admin/admin_plans_index', $data);
    }

    /**
     * Show create plan form (GET request)
     *
     * @return string
     */
    public function create()
    {
        $data = [
            'page_title' => 'Create Plan',
            'breadcrumbs' => [
                ['title' => 'Dashboard', 'url' => site_url('dashboard')],
                ['title' => 'Administrator Settings', 'url' => site_url('admin')],
                ['title' => 'Plans Management', 'url' => site_url('admin/plans')],
                ['title' => 'Create Plan', 'url' => '']
            ]
        ];

        return view('admin/admin_plans_create', $data);
    }

    /**
     * Store new plan (POST request)
     *
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function store()
    {
        // Mock response - in real implementation, this would save to database
        return redirect()->to(site_url('admin/plans'))
                        ->with('success', 'Plan created successfully!');
    }

    /**
     * Show edit plan form (GET request)
     *
     * @param int $id
     * @return string
     */
    public function edit($id)
    {
        $plans = $this->getDummyPlans();
        $plan = array_filter($plans, function($p) use ($id) {
            return $p['id'] == $id;
        });
        $plan = reset($plan);

        if (!$plan) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Plan not found');
        }

        $data = [
            'page_title' => 'Edit Plan',
            'breadcrumbs' => [
                ['title' => 'Dashboard', 'url' => site_url('dashboard')],
                ['title' => 'Administrator Settings', 'url' => site_url('admin')],
                ['title' => 'Plans Management', 'url' => site_url('admin/plans')],
                ['title' => 'Edit Plan', 'url' => '']
            ],
            'plan' => $plan
        ];

        return view('admin/admin_plans_edit', $data);
    }

    /**
     * Update plan (PUT request)
     *
     * @param int $id
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function update($id)
    {
        // Mock response - in real implementation, this would update the database
        return redirect()->to(site_url('admin/plans'))
                        ->with('success', 'Plan updated successfully!');
    }

    /**
     * Delete plan (DELETE request)
     *
     * @param int $id
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function delete($id)
    {
        // Mock response - in real implementation, this would delete from database
        return redirect()->to(site_url('admin/plans'))
                        ->with('success', 'Plan deleted successfully!');
    }

    /**
     * Display programs for Development Plan or KRAs for Corporate Plan (GET request)
     *
     * @param int $planId
     * @return string
     */
    public function components($planId)
    {
        $plans = $this->getDummyPlans();
        $plan = array_filter($plans, function($p) use ($planId) {
            return $p['id'] == $planId;
        });
        $plan = reset($plan);

        if (!$plan) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Plan not found');
        }

        if ($plan['type'] === 'Development') {
            return $this->programs($planId);
        } else {
            return $this->kras($planId);
        }
    }

    /**
     * Display programs for a Development Plan (GET request)
     *
     * @param int $planId
     * @return string
     */
    public function programs($planId)
    {
        $plans = $this->getDummyPlans();
        $plan = array_filter($plans, function($p) use ($planId) {
            return $p['id'] == $planId;
        });
        $plan = reset($plan);

        if (!$plan || $plan['type'] !== 'Development') {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Development Plan not found');
        }

        $data = [
            'page_title' => 'Programs - ' . $plan['name'],
            'breadcrumbs' => [
                ['title' => 'Dashboard', 'url' => site_url('dashboard')],
                ['title' => 'Administrator Settings', 'url' => site_url('admin')],
                ['title' => 'Plans Management', 'url' => site_url('admin/plans')],
                ['title' => $plan['name'], 'url' => '']
            ],
            'plan' => $plan,
            'programs' => $this->getDummyPrograms($planId)
        ];

        return view('admin/admin_plans_programs', $data);
    }

    /**
     * Display KRAs for a Corporate Plan (GET request)
     *
     * @param int $planId
     * @return string
     */
    public function kras($planId)
    {
        $plans = $this->getDummyPlans();
        $plan = array_filter($plans, function($p) use ($planId) {
            return $p['id'] == $planId;
        });
        $plan = reset($plan);

        if (!$plan || $plan['type'] !== 'Corporate') {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Corporate Plan not found');
        }

        $data = [
            'page_title' => 'KRAs - ' . $plan['name'],
            'breadcrumbs' => [
                ['title' => 'Dashboard', 'url' => site_url('dashboard')],
                ['title' => 'Administrator Settings', 'url' => site_url('admin')],
                ['title' => 'Plans Management', 'url' => site_url('admin/plans')],
                ['title' => $plan['name'], 'url' => '']
            ],
            'plan' => $plan,
            'kras' => $this->getDummyKRAs($planId)
        ];

        return view('admin/admin_plans_kras', $data);
    }

    /**
     * Show create program form (GET request)
     *
     * @param int $planId
     * @return string
     */
    public function createProgram($planId)
    {
        $plans = $this->getDummyPlans();
        $plan = array_filter($plans, function($p) use ($planId) {
            return $p['id'] == $planId;
        });
        $plan = reset($plan);

        if (!$plan || $plan['type'] !== 'Development') {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Development Plan not found');
        }

        $data = [
            'page_title' => 'Create Program',
            'breadcrumbs' => [
                ['title' => 'Dashboard', 'url' => site_url('dashboard')],
                ['title' => 'Administrator Settings', 'url' => site_url('admin')],
                ['title' => 'Plans Management', 'url' => site_url('admin/plans')],
                ['title' => $plan['name'], 'url' => site_url('admin/plans/' . $planId . '/programs')],
                ['title' => 'Create Program', 'url' => '']
            ],
            'plan' => $plan
        ];

        return view('admin/admin_plans_program_create', $data);
    }

    /**
     * Store new program (POST request)
     *
     * @param int $planId
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function storeProgram($planId)
    {
        // Mock response - in real implementation, this would save to database
        return redirect()->to(site_url('admin/plans/' . $planId . '/programs'))
                        ->with('success', 'Program created successfully!');
    }

    /**
     * Show create KRA form (GET request)
     *
     * @param int $planId
     * @return string
     */
    public function createKRA($planId)
    {
        $plans = $this->getDummyPlans();
        $plan = array_filter($plans, function($p) use ($planId) {
            return $p['id'] == $planId;
        });
        $plan = reset($plan);

        if (!$plan || $plan['type'] !== 'Corporate') {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Corporate Plan not found');
        }

        $data = [
            'page_title' => 'Create KRA',
            'breadcrumbs' => [
                ['title' => 'Dashboard', 'url' => site_url('dashboard')],
                ['title' => 'Administrator Settings', 'url' => site_url('admin')],
                ['title' => 'Plans Management', 'url' => site_url('admin/plans')],
                ['title' => $plan['name'], 'url' => site_url('admin/plans/' . $planId . '/kras')],
                ['title' => 'Create KRA', 'url' => '']
            ],
            'plan' => $plan
        ];

        return view('admin/admin_plans_kra_create', $data);
    }

    /**
     * Store new KRA (POST request)
     *
     * @param int $planId
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function storeKRA($planId)
    {
        // Mock response - in real implementation, this would save to database
        return redirect()->to(site_url('admin/plans/' . $planId . '/kras'))
                        ->with('success', 'KRA created successfully!');
    }

    /**
     * Display projects for a specific program (GET request)
     *
     * @param int $programId
     * @return string
     */
    public function projects($programId)
    {
        $programs = $this->getAllDummyPrograms();
        $program = array_filter($programs, function($p) use ($programId) {
            return $p['id'] == $programId;
        });
        $program = reset($program);

        if (!$program) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Program not found');
        }

        $plans = $this->getDummyPlans();
        $plan = array_filter($plans, function($p) use ($program) {
            return $p['id'] == $program['plan_id'];
        });
        $plan = reset($plan);

        $data = [
            'page_title' => 'Projects - ' . $program['name'],
            'breadcrumbs' => [
                ['title' => 'Dashboard', 'url' => site_url('dashboard')],
                ['title' => 'Administrator Settings', 'url' => site_url('admin')],
                ['title' => 'Plans Management', 'url' => site_url('admin/plans')],
                ['title' => $plan['name'], 'url' => site_url('admin/plans/' . $program['plan_id'] . '/programs')],
                ['title' => $program['name'], 'url' => '']
            ],
            'plan' => $plan,
            'program' => $program,
            'projects' => $this->getDummyProjects($programId)
        ];

        return view('admin/admin_plans_projects', $data);
    }

    /**
     * Display KPIs for a specific KRA (GET request)
     *
     * @param int $kraId
     * @return string
     */
    public function kpis($kraId)
    {
        $kras = $this->getAllDummyKRAs();
        $kra = array_filter($kras, function($k) use ($kraId) {
            return $k['id'] == $kraId;
        });
        $kra = reset($kra);

        if (!$kra) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('KRA not found');
        }

        $plans = $this->getDummyPlans();
        $plan = array_filter($plans, function($p) use ($kra) {
            return $p['id'] == $kra['plan_id'];
        });
        $plan = reset($plan);

        $data = [
            'page_title' => 'KPIs - ' . $kra['name'],
            'breadcrumbs' => [
                ['title' => 'Dashboard', 'url' => site_url('dashboard')],
                ['title' => 'Administrator Settings', 'url' => site_url('admin')],
                ['title' => 'Plans Management', 'url' => site_url('admin/plans')],
                ['title' => $plan['name'], 'url' => site_url('admin/plans/' . $kra['plan_id'] . '/kras')],
                ['title' => $kra['name'], 'url' => '']
            ],
            'plan' => $plan,
            'kra' => $kra,
            'kpis' => $this->getDummyKPIs($kraId)
        ];

        return view('admin/admin_plans_kpis', $data);
    }

    /**
     * Show create project form (GET request)
     *
     * @param int $programId
     * @return string
     */
    public function createProject($programId)
    {
        $programs = $this->getAllDummyPrograms();
        $program = array_filter($programs, function($p) use ($programId) {
            return $p['id'] == $programId;
        });
        $program = reset($program);

        if (!$program) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Program not found');
        }

        $plans = $this->getDummyPlans();
        $plan = array_filter($plans, function($p) use ($program) {
            return $p['id'] == $program['plan_id'];
        });
        $plan = reset($plan);

        $data = [
            'page_title' => 'Create Project',
            'breadcrumbs' => [
                ['title' => 'Dashboard', 'url' => site_url('dashboard')],
                ['title' => 'Administrator Settings', 'url' => site_url('admin')],
                ['title' => 'Plans Management', 'url' => site_url('admin/plans')],
                ['title' => $plan['name'], 'url' => site_url('admin/plans/' . $program['plan_id'] . '/programs')],
                ['title' => $program['name'], 'url' => site_url('admin/programs/' . $programId . '/projects')],
                ['title' => 'Create Project', 'url' => '']
            ],
            'plan' => $plan,
            'program' => $program
        ];

        return view('admin/admin_plans_project_create', $data);
    }

    /**
     * Store new project (POST request)
     *
     * @param int $programId
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function storeProject($programId)
    {
        // Mock response - in real implementation, this would save to database
        return redirect()->to(site_url('admin/programs/' . $programId . '/projects'))
                        ->with('success', 'Project created successfully!');
    }

    /**
     * Show create KPI form (GET request)
     *
     * @param int $kraId
     * @return string
     */
    public function createKPI($kraId)
    {
        $kras = $this->getAllDummyKRAs();
        $kra = array_filter($kras, function($k) use ($kraId) {
            return $k['id'] == $kraId;
        });
        $kra = reset($kra);

        if (!$kra) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('KRA not found');
        }

        $plans = $this->getDummyPlans();
        $plan = array_filter($plans, function($p) use ($kra) {
            return $p['id'] == $kra['plan_id'];
        });
        $plan = reset($plan);

        $data = [
            'page_title' => 'Create KPI',
            'breadcrumbs' => [
                ['title' => 'Dashboard', 'url' => site_url('dashboard')],
                ['title' => 'Administrator Settings', 'url' => site_url('admin')],
                ['title' => 'Plans Management', 'url' => site_url('admin/plans')],
                ['title' => $plan['name'], 'url' => site_url('admin/plans/' . $kra['plan_id'] . '/kras')],
                ['title' => $kra['name'], 'url' => site_url('admin/kras/' . $kraId . '/kpis')],
                ['title' => 'Create KPI', 'url' => '']
            ],
            'plan' => $plan,
            'kra' => $kra
        ];

        return view('admin/admin_plans_kpi_create', $data);
    }

    /**
     * Store new KPI (POST request)
     *
     * @param int $kraId
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function storeKPI($kraId)
    {
        // Mock response - in real implementation, this would save to database
        return redirect()->to(site_url('admin/kras/' . $kraId . '/kpis'))
                        ->with('success', 'KPI created successfully!');
    }

    // ========================================
    // DUMMY DATA METHODS
    // ========================================

    /**
     * Get dummy plans data
     *
     * @return array
     */
    private function getDummyPlans()
    {
        return [
            [
                'id' => 1,
                'name' => 'Papua New Guinea Vision 2050',
                'type' => 'Development',
                'fiscal_year' => 2024,
                'description' => 'Long-term development plan for Papua New Guinea',
                'status' => 'Active',
                'created_at' => '2024-01-15 10:30:00'
            ],
            [
                'id' => 2,
                'name' => 'Medium Term Development Plan IV (MTDP IV)',
                'type' => 'Development',
                'fiscal_year' => 2024,
                'description' => 'Five-year development strategy for PNG',
                'status' => 'Active',
                'created_at' => '2024-01-20 14:15:00'
            ],
            [
                'id' => 3,
                'name' => 'Treasury Corporate Plan 2024',
                'type' => 'Corporate',
                'fiscal_year' => 2024,
                'description' => 'Department of Treasury strategic objectives',
                'status' => 'Active',
                'created_at' => '2024-02-01 09:45:00'
            ],
            [
                'id' => 4,
                'name' => 'Finance Corporate Plan 2024',
                'type' => 'Corporate',
                'fiscal_year' => 2024,
                'description' => 'Ministry of Finance strategic plan',
                'status' => 'Draft',
                'created_at' => '2024-02-10 16:20:00'
            ],
            [
                'id' => 5,
                'name' => 'Infrastructure Development Plan',
                'type' => 'Development',
                'fiscal_year' => 2024,
                'description' => 'National infrastructure development strategy',
                'status' => 'Active',
                'created_at' => '2024-02-15 11:30:00'
            ]
        ];
    }

    /**
     * Get plans statistics
     *
     * @return array
     */
    private function getPlansStats()
    {
        return [
            'total_plans' => 5,
            'development_plans' => 3,
            'corporate_plans' => 2,
            'active_plans' => 4,
            'draft_plans' => 1
        ];
    }

    /**
     * Get dummy programs data for a specific plan
     *
     * @param int $planId
     * @return array
     */
    private function getDummyPrograms($planId)
    {
        $allPrograms = $this->getAllDummyPrograms();

        return array_filter($allPrograms, function($program) use ($planId) {
            return $program['plan_id'] == $planId;
        });
    }

    /**
     * Get all dummy programs data
     *
     * @return array
     */
    private function getAllDummyPrograms()
    {
        return [
            // Vision 2050 Programs
            [
                'id' => 1,
                'plan_id' => 1,
                'name' => 'Economic Development Program',
                'description' => 'Sustainable economic growth and development',
                'budget' => 500000000,
                'status' => 'Active',
                'start_date' => '2024-01-01',
                'end_date' => '2026-12-31',
                'created_at' => '2024-01-15 11:00:00'
            ],
            [
                'id' => 2,
                'plan_id' => 1,
                'name' => 'Social Development Program',
                'description' => 'Improving quality of life for all citizens',
                'budget' => 300000000,
                'status' => 'Active',
                'start_date' => '2024-01-01',
                'end_date' => '2026-12-31',
                'created_at' => '2024-01-15 11:15:00'
            ],
            // MTDP IV Programs
            [
                'id' => 3,
                'plan_id' => 2,
                'name' => 'Infrastructure Development Program',
                'description' => 'Building critical infrastructure nationwide',
                'budget' => 800000000,
                'status' => 'Active',
                'start_date' => '2024-01-01',
                'end_date' => '2028-12-31',
                'created_at' => '2024-01-20 14:30:00'
            ],
            [
                'id' => 4,
                'plan_id' => 2,
                'name' => 'Human Resource Development Program',
                'description' => 'Capacity building and skills development',
                'budget' => 200000000,
                'status' => 'Active',
                'start_date' => '2024-01-01',
                'end_date' => '2028-12-31',
                'created_at' => '2024-01-20 14:45:00'
            ],
            // Infrastructure Development Plan Programs
            [
                'id' => 5,
                'plan_id' => 5,
                'name' => 'Transport Infrastructure Program',
                'description' => 'Roads, bridges, and transport networks',
                'budget' => 600000000,
                'status' => 'Active',
                'start_date' => '2024-02-01',
                'end_date' => '2027-12-31',
                'created_at' => '2024-02-15 12:00:00'
            ]
        ];
    }

    /**
     * Get dummy KRAs data for a specific plan
     *
     * @param int $planId
     * @return array
     */
    private function getDummyKRAs($planId)
    {
        $allKRAs = $this->getAllDummyKRAs();

        return array_filter($allKRAs, function($kra) use ($planId) {
            return $kra['plan_id'] == $planId;
        });
    }

    /**
     * Get all dummy KRAs data
     *
     * @return array
     */
    private function getAllDummyKRAs()
    {
        return [
            // Treasury Corporate Plan KRAs
            [
                'id' => 1,
                'plan_id' => 3,
                'name' => 'Financial Management Excellence',
                'description' => 'Ensure effective financial management and accountability',
                'weight' => 30,
                'status' => 'Active',
                'created_at' => '2024-02-01 10:00:00'
            ],
            [
                'id' => 2,
                'plan_id' => 3,
                'name' => 'Policy Development and Implementation',
                'description' => 'Develop and implement sound fiscal policies',
                'weight' => 25,
                'status' => 'Active',
                'created_at' => '2024-02-01 10:15:00'
            ],
            [
                'id' => 3,
                'plan_id' => 3,
                'name' => 'Stakeholder Engagement',
                'description' => 'Effective engagement with government and external stakeholders',
                'weight' => 20,
                'status' => 'Active',
                'created_at' => '2024-02-01 10:30:00'
            ],
            [
                'id' => 4,
                'plan_id' => 3,
                'name' => 'Organizational Development',
                'description' => 'Build organizational capacity and capability',
                'weight' => 25,
                'status' => 'Active',
                'created_at' => '2024-02-01 10:45:00'
            ],
            // Finance Corporate Plan KRAs
            [
                'id' => 5,
                'plan_id' => 4,
                'name' => 'Revenue Management',
                'description' => 'Optimize revenue collection and management',
                'weight' => 35,
                'status' => 'Draft',
                'created_at' => '2024-02-10 16:30:00'
            ],
            [
                'id' => 6,
                'plan_id' => 4,
                'name' => 'Budget Planning and Control',
                'description' => 'Effective budget planning and expenditure control',
                'weight' => 30,
                'status' => 'Draft',
                'created_at' => '2024-02-10 16:45:00'
            ]
        ];
    }

    /**
     * Get dummy projects data for a specific program
     *
     * @param int $programId
     * @return array
     */
    private function getDummyProjects($programId)
    {
        $allProjects = [
            // Economic Development Program Projects
            [
                'id' => 1,
                'program_id' => 1,
                'name' => 'Small Business Development Initiative',
                'description' => 'Support for small and medium enterprises',
                'budget' => 50000000,
                'status' => 'Active',
                'start_date' => '2024-03-01',
                'end_date' => '2025-12-31',
                'progress' => 25,
                'created_at' => '2024-01-15 12:00:00'
            ],
            [
                'id' => 2,
                'program_id' => 1,
                'name' => 'Export Promotion Project',
                'description' => 'Boost PNG exports to international markets',
                'budget' => 75000000,
                'status' => 'Active',
                'start_date' => '2024-02-01',
                'end_date' => '2026-06-30',
                'progress' => 40,
                'created_at' => '2024-01-15 12:15:00'
            ],
            // Social Development Program Projects
            [
                'id' => 3,
                'program_id' => 2,
                'name' => 'Healthcare Infrastructure Project',
                'description' => 'Build and upgrade healthcare facilities',
                'budget' => 120000000,
                'status' => 'Active',
                'start_date' => '2024-01-15',
                'end_date' => '2026-12-31',
                'progress' => 15,
                'created_at' => '2024-01-15 12:30:00'
            ],
            [
                'id' => 4,
                'program_id' => 2,
                'name' => 'Education Quality Improvement',
                'description' => 'Enhance quality of education nationwide',
                'budget' => 80000000,
                'status' => 'Active',
                'start_date' => '2024-02-01',
                'end_date' => '2026-12-31',
                'progress' => 30,
                'created_at' => '2024-01-15 12:45:00'
            ],
            // Infrastructure Development Program Projects
            [
                'id' => 5,
                'program_id' => 3,
                'name' => 'Highlands Highway Upgrade',
                'description' => 'Major highway infrastructure improvement',
                'budget' => 300000000,
                'status' => 'Active',
                'start_date' => '2024-01-01',
                'end_date' => '2027-12-31',
                'progress' => 20,
                'created_at' => '2024-01-20 15:00:00'
            ],
            [
                'id' => 6,
                'program_id' => 3,
                'name' => 'Port Moresby Water Supply',
                'description' => 'Improve water supply infrastructure',
                'budget' => 150000000,
                'status' => 'Planning',
                'start_date' => '2024-06-01',
                'end_date' => '2026-12-31',
                'progress' => 5,
                'created_at' => '2024-01-20 15:15:00'
            ]
        ];

        return array_filter($allProjects, function($project) use ($programId) {
            return $project['program_id'] == $programId;
        });
    }

    /**
     * Get dummy KPIs data for a specific KRA
     *
     * @param int $kraId
     * @return array
     */
    private function getDummyKPIs($kraId)
    {
        $allKPIs = [
            // Financial Management Excellence KPIs
            [
                'id' => 1,
                'kra_id' => 1,
                'name' => 'Budget Variance',
                'description' => 'Percentage variance between budgeted and actual expenditure',
                'target' => '< 5%',
                'measurement' => 'Percentage',
                'frequency' => 'Monthly',
                'current_value' => '3.2%',
                'status' => 'On Track',
                'created_at' => '2024-02-01 11:00:00'
            ],
            [
                'id' => 2,
                'kra_id' => 1,
                'name' => 'Financial Report Timeliness',
                'description' => 'Percentage of financial reports submitted on time',
                'target' => '95%',
                'measurement' => 'Percentage',
                'frequency' => 'Monthly',
                'current_value' => '92%',
                'status' => 'Needs Attention',
                'created_at' => '2024-02-01 11:15:00'
            ],
            // Policy Development and Implementation KPIs
            [
                'id' => 3,
                'kra_id' => 2,
                'name' => 'Policy Implementation Rate',
                'description' => 'Percentage of policies implemented within timeframe',
                'target' => '90%',
                'measurement' => 'Percentage',
                'frequency' => 'Quarterly',
                'current_value' => '85%',
                'status' => 'Needs Attention',
                'created_at' => '2024-02-01 11:30:00'
            ],
            [
                'id' => 4,
                'kra_id' => 2,
                'name' => 'Stakeholder Consultation Score',
                'description' => 'Average satisfaction score from stakeholder consultations',
                'target' => '4.0/5.0',
                'measurement' => 'Score',
                'frequency' => 'Quarterly',
                'current_value' => '4.2/5.0',
                'status' => 'Exceeding',
                'created_at' => '2024-02-01 11:45:00'
            ],
            // Stakeholder Engagement KPIs
            [
                'id' => 5,
                'kra_id' => 3,
                'name' => 'Stakeholder Meeting Frequency',
                'description' => 'Number of stakeholder meetings conducted per quarter',
                'target' => '12',
                'measurement' => 'Count',
                'frequency' => 'Quarterly',
                'current_value' => '10',
                'status' => 'Needs Attention',
                'created_at' => '2024-02-01 12:00:00'
            ],
            // Organizational Development KPIs
            [
                'id' => 6,
                'kra_id' => 4,
                'name' => 'Staff Training Hours',
                'description' => 'Average training hours per staff member per year',
                'target' => '40 hours',
                'measurement' => 'Hours',
                'frequency' => 'Annual',
                'current_value' => '35 hours',
                'status' => 'On Track',
                'created_at' => '2024-02-01 12:15:00'
            ]
        ];

        return array_filter($allKPIs, function($kpi) use ($kraId) {
            return $kpi['kra_id'] == $kraId;
        });
    }
}