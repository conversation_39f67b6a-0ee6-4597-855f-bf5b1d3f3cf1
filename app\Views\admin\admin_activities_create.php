<?= $this->extend('templates/admin_template') ?>

<?= $this->section('title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('page_title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
    .admin-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .breadcrumb-item a {
        color: #800000;
        text-decoration: none;
    }

    .breadcrumb-item a:hover {
        color: #28a745;
    }

    .form-label {
        font-weight: 600;
        color: #495057;
    }

    .required {
        color: #dc3545;
    }

    .form-section {
        border-left: 4px solid #28a745;
        padding-left: 1rem;
        margin-bottom: 2rem;
    }

    .legislation-info {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border-radius: 10px;
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .group-checkbox {
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 0.75rem;
        margin-bottom: 0.5rem;
        transition: all 0.3s ease;
    }

    .group-checkbox:hover {
        background-color: #f8f9fa;
        border-color: #28a745;
    }

    .group-checkbox input:checked + label {
        color: #28a745;
        font-weight: 600;
    }

    .priority-option {
        border: 2px solid #dee2e6;
        border-radius: 10px;
        padding: 1rem;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .priority-option:hover {
        border-color: #28a745;
        background-color: #f8f9fa;
    }

    .priority-option input:checked + .priority-content {
        border-color: #28a745;
        background-color: #e8f5e9;
    }

    .priority-critical { border-color: #dc3545; }
    .priority-high { border-color: #ffc107; }
    .priority-medium { border-color: #17a2b8; }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="fade-in">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <?php foreach ($breadcrumbs as $index => $breadcrumb): ?>
                        <?php if ($index === count($breadcrumbs) - 1): ?>
                            <li class="breadcrumb-item active" aria-current="page"><?= esc($breadcrumb['title']) ?></li>
                        <?php else: ?>
                            <li class="breadcrumb-item">
                                <a href="<?= esc($breadcrumb['url']) ?>"><?= esc($breadcrumb['title']) ?></a>
                            </li>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Legislation Information -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="legislation-info">
                <h5 class="fw-bold mb-1" style="color: #800000;">
                    <i class="fas fa-balance-scale me-2"></i><?= esc($legislation['name']) ?>
                </h5>
                <p class="text-muted mb-1"><?= esc($legislation['description']) ?></p>
                <div class="d-flex gap-3">
                    <span class="badge bg-primary"><?= esc($legislation['reference_number']) ?></span>
                    <span class="badge bg-success"><?= esc($legislation['status']) ?></span>
                </div>
            </div>
        </div>
    </div>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="admin-card p-4">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h3 class="mb-2 text-success">
                            <i class="fas fa-plus-circle me-2"></i>Create New Activity
                        </h3>
                        <p class="text-muted mb-0">
                            Add a mandatory activity under this legislation that will be linked to organizational groups.
                        </p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <a href="<?= site_url('admin/legislation/' . $legislation['id'] . '/activities') ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Back to Activities
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Create Activity Form -->
    <form action="<?= site_url('admin/legislation/' . $legislation['id'] . '/activities') ?>" method="POST" id="createActivityForm">
        <?= csrf_field() ?>
        
        <div class="row">
            <!-- Activity Details -->
            <div class="col-lg-8">
                <div class="admin-card p-4 mb-4">
                    <!-- Basic Information Section -->
                    <div class="form-section">
                        <h5 class="fw-bold mb-3" style="color: #800000;">
                            <i class="fas fa-info-circle me-2"></i>Activity Information
                        </h5>
                        
                        <div class="mb-3">
                            <label for="name" class="form-label">
                                Activity Name <span class="required">*</span>
                            </label>
                            <input type="text" class="form-control" id="name" name="name" required
                                   placeholder="e.g., Annual Performance Review">
                            <div class="form-text">Enter a clear, descriptive name for this mandatory activity</div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">
                                Description <span class="required">*</span>
                            </label>
                            <textarea class="form-control" id="description" name="description" rows="3" required
                                      placeholder="Detailed description of what this activity involves"></textarea>
                            <div class="form-text">Provide a comprehensive description of the activity requirements</div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="frequency" class="form-label">
                                    Frequency <span class="required">*</span>
                                </label>
                                <select class="form-select" id="frequency" name="frequency" required>
                                    <option value="">Select Frequency</option>
                                    <option value="Annual">Annual</option>
                                    <option value="Quarterly">Quarterly</option>
                                    <option value="Monthly">Monthly</option>
                                    <option value="Bi-Annual">Bi-Annual</option>
                                    <option value="As Required">As Required</option>
                                </select>
                                <div class="form-text">How often this activity must be performed</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="due_period" class="form-label">
                                    Due Period <span class="required">*</span>
                                </label>
                                <input type="text" class="form-control" id="due_period" name="due_period" required
                                       placeholder="e.g., December, End of Quarter">
                                <div class="form-text">When this activity is typically due</div>
                            </div>
                        </div>
                    </div>

                    <!-- Additional Details Section -->
                    <div class="form-section">
                        <h5 class="fw-bold mb-3" style="color: #800000;">
                            <i class="fas fa-cogs me-2"></i>Additional Details
                        </h5>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="estimated_duration" class="form-label">Estimated Duration</label>
                                <input type="text" class="form-control" id="estimated_duration" name="estimated_duration"
                                       placeholder="e.g., 2 weeks, 1 month">
                                <div class="form-text">How long this activity typically takes to complete</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="resources_required" class="form-label">Resources Required</label>
                                <input type="text" class="form-control" id="resources_required" name="resources_required"
                                       placeholder="e.g., HR Staff, External Consultant">
                                <div class="form-text">What resources are needed for this activity</div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="deliverables" class="form-label">Expected Deliverables</label>
                            <textarea class="form-control" id="deliverables" name="deliverables" rows="2"
                                      placeholder="List the expected outputs or deliverables"></textarea>
                            <div class="form-text">What should be produced or delivered upon completion</div>
                        </div>

                        <div class="mb-3">
                            <label for="compliance_notes" class="form-label">Compliance Notes</label>
                            <textarea class="form-control" id="compliance_notes" name="compliance_notes" rows="2"
                                      placeholder="Any specific compliance requirements or notes"></textarea>
                            <div class="form-text">Additional compliance requirements or important notes</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Priority and Groups -->
            <div class="col-lg-4">
                <!-- Priority Selection -->
                <div class="admin-card p-4 mb-4">
                    <h5 class="fw-bold mb-3" style="color: #800000;">
                        <i class="fas fa-exclamation-triangle me-2"></i>Priority Level
                    </h5>
                    
                    <div class="row g-3">
                        <div class="col-12">
                            <label class="priority-option priority-critical">
                                <input type="radio" name="priority" value="Critical" class="d-none">
                                <div class="priority-content">
                                    <i class="fas fa-exclamation-triangle text-danger fs-4 mb-2"></i>
                                    <h6 class="fw-bold text-danger">Critical</h6>
                                    <small class="text-muted">Must be completed</small>
                                </div>
                            </label>
                        </div>
                        <div class="col-12">
                            <label class="priority-option priority-high">
                                <input type="radio" name="priority" value="High" class="d-none">
                                <div class="priority-content">
                                    <i class="fas fa-arrow-up text-warning fs-4 mb-2"></i>
                                    <h6 class="fw-bold text-warning">High</h6>
                                    <small class="text-muted">Important priority</small>
                                </div>
                            </label>
                        </div>
                        <div class="col-12">
                            <label class="priority-option priority-medium">
                                <input type="radio" name="priority" value="Medium" class="d-none">
                                <div class="priority-content">
                                    <i class="fas fa-minus text-info fs-4 mb-2"></i>
                                    <h6 class="fw-bold text-info">Medium</h6>
                                    <small class="text-muted">Standard priority</small>
                                </div>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Group Selection -->
                <div class="admin-card p-4">
                    <h5 class="fw-bold mb-3" style="color: #800000;">
                        <i class="fas fa-users me-2"></i>Linked Groups <span class="required">*</span>
                    </h5>
                    <p class="text-muted small mb-3">Select which organizational groups must perform this activity:</p>
                    
                    <div class="group-selection">
                        <?php foreach ($groups as $group): ?>
                        <div class="group-checkbox">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="linked_groups[]" 
                                       value="<?= $group['id'] ?>" id="group_<?= $group['id'] ?>">
                                <label class="form-check-label w-100" for="group_<?= $group['id'] ?>">
                                    <strong><?= esc($group['name']) ?></strong>
                                    <br><small class="text-muted">Level <?= $group['level'] ?></small>
                                </label>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                    
                    <div class="form-text mt-2">
                        <i class="fas fa-info-circle me-1"></i>
                        Select at least one group that will be responsible for this activity
                    </div>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="admin-card p-4">
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save me-2"></i>Create Activity
                        </button>
                        <a href="<?= site_url('admin/legislation/' . $legislation['id'] . '/activities') ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                        <button type="button" class="btn btn-outline-info ms-auto" onclick="previewActivity()">
                            <i class="fas fa-eye me-2"></i>Preview
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Priority selection handling
        document.querySelectorAll('input[name="priority"]').forEach(radio => {
            radio.addEventListener('change', function() {
                // Remove active state from all options
                document.querySelectorAll('.priority-option').forEach(option => {
                    option.classList.remove('active');
                });
                
                // Add active state to selected option
                this.closest('.priority-option').classList.add('active');
            });
        });

        // Form validation
        document.getElementById('createActivityForm').addEventListener('submit', function(e) {
            const name = document.getElementById('name').value.trim();
            const description = document.getElementById('description').value.trim();
            const frequency = document.getElementById('frequency').value;
            const duePeriod = document.getElementById('due_period').value.trim();
            const priority = document.querySelector('input[name="priority"]:checked');
            const linkedGroups = document.querySelectorAll('input[name="linked_groups[]"]:checked');

            if (!name || !description || !frequency || !duePeriod) {
                e.preventDefault();
                alert('Please fill in all required fields.');
                return false;
            }

            if (!priority) {
                e.preventDefault();
                alert('Please select a priority level.');
                return false;
            }

            if (linkedGroups.length === 0) {
                e.preventDefault();
                alert('Please select at least one group for this activity.');
                return false;
            }
        });

        console.log('Create Activity form initialized successfully');
    });

    function previewActivity() {
        const name = document.getElementById('name').value;
        const description = document.getElementById('description').value;
        const frequency = document.getElementById('frequency').value;
        const duePeriod = document.getElementById('due_period').value;
        const priority = document.querySelector('input[name="priority"]:checked');
        const linkedGroups = document.querySelectorAll('input[name="linked_groups[]"]:checked');
        
        let preview = `Activity Preview:\n\n`;
        preview += `Name: ${name}\n`;
        preview += `Description: ${description}\n`;
        preview += `Frequency: ${frequency}\n`;
        preview += `Due Period: ${duePeriod}\n`;
        preview += `Priority: ${priority ? priority.value : 'Not selected'}\n`;
        preview += `Linked Groups: ${Array.from(linkedGroups).map(g => g.nextElementSibling.textContent.trim()).join(', ')}\n`;
        
        alert(preview);
    }
</script>
<?= $this->endSection() ?>
