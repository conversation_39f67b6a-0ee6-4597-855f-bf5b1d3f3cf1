<?= $this->extend('templates/admin_template') ?>

<?= $this->section('title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('page_title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
    .admin-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .breadcrumb-item a {
        color: #800000;
        text-decoration: none;
    }

    .breadcrumb-item a:hover {
        color: #28a745;
    }

    .form-label {
        color: #800000;
        font-weight: 600;
    }

    .form-control:focus {
        border-color: #28a745;
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
    }

    .btn-success {
        background-color: #28a745;
        border-color: #28a745;
    }

    .btn-success:hover {
        background-color: #218838;
        border-color: #1e7e34;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="fade-in">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <?php foreach ($breadcrumbs as $index => $breadcrumb): ?>
                        <?php if ($index === count($breadcrumbs) - 1): ?>
                            <li class="breadcrumb-item active" aria-current="page"><?= esc($breadcrumb['title']) ?></li>
                        <?php else: ?>
                            <li class="breadcrumb-item">
                                <a href="<?= esc($breadcrumb['url']) ?>"><?= esc($breadcrumb['title']) ?></a>
                            </li>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="admin-card p-4">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h3 class="mb-2 text-success">
                            <i class="fas fa-plus-circle me-2"></i>Create New Structure
                        </h3>
                        <p class="text-muted mb-0">
                            Create a new organizational structure to define your organization's hierarchy.
                        </p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <a href="<?= site_url('admin/structures') ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Back to Structures
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Create Form -->
    <div class="row">
        <div class="col-lg-8 col-md-10 mx-auto">
            <div class="admin-card p-4">
                <form method="POST" action="<?= site_url('admin/structures') ?>" id="createStructureForm">
                    <?= csrf_field() ?>
                    
                    <div class="mb-4">
                        <label for="name" class="form-label">
                            <i class="fas fa-building me-2"></i>Structure Name *
                        </label>
                        <input type="text" 
                               class="form-control form-control-lg" 
                               id="name" 
                               name="name" 
                               placeholder="Enter structure name (e.g., Papua New Guinea Government Structure)"
                               required
                               maxlength="255">
                        <div class="form-text">
                            Enter a descriptive name for your organizational structure. This will be the main identifier.
                        </div>
                    </div>

                    <div class="mb-4">
                        <div class="form-check">
                            <input class="form-check-input" 
                                   type="checkbox" 
                                   id="is_active" 
                                   name="is_active" 
                                   value="1">
                            <label class="form-check-label" for="is_active">
                                <strong>Set as Active Structure</strong>
                            </label>
                        </div>
                        <div class="form-text">
                            Check this box to make this the active organizational structure. Only one structure can be active at a time.
                        </div>
                    </div>

                    <div class="mb-4">
                        <div class="alert alert-info">
                            <h6 class="alert-heading">
                                <i class="fas fa-info-circle me-2"></i>Next Steps
                            </h6>
                            <p class="mb-0">
                                After creating the structure, you'll be able to:
                            </p>
                            <ul class="mb-0 mt-2">
                                <li>Add groups (departments, divisions, units) to the structure</li>
                                <li>Create positions within each group</li>
                                <li>Define reporting relationships and hierarchies</li>
                                <li>Assign roles and permissions to positions</li>
                            </ul>
                        </div>
                    </div>

                    <div class="d-flex gap-3">
                        <button type="submit" class="btn btn-success btn-lg">
                            <i class="fas fa-save me-2"></i>Create Structure
                        </button>
                        <a href="<?= site_url('admin/structures') ?>" class="btn btn-outline-secondary btn-lg">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Help Section -->
    <div class="row mt-4">
        <div class="col-lg-8 col-md-10 mx-auto">
            <div class="admin-card p-3">
                <h6 class="mb-2 text-primary">
                    <i class="fas fa-question-circle me-2"></i>Structure Guidelines
                </h6>
                <div class="row">
                    <div class="col-md-6">
                        <ul class="list-unstyled small text-muted">
                            <li><i class="fas fa-check text-success me-2"></i>Use descriptive, clear names</li>
                            <li><i class="fas fa-check text-success me-2"></i>Consider your organization's scope</li>
                            <li><i class="fas fa-check text-success me-2"></i>Plan for future growth</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <ul class="list-unstyled small text-muted">
                            <li><i class="fas fa-lightbulb text-warning me-2"></i>Examples: "Ministry Structure", "Department Structure"</li>
                            <li><i class="fas fa-lightbulb text-warning me-2"></i>Keep names under 255 characters</li>
                            <li><i class="fas fa-lightbulb text-warning me-2"></i>Use consistent naming conventions</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.getElementById('createStructureForm');
        const nameInput = document.getElementById('name');
        
        // Form validation
        form.addEventListener('submit', function(e) {
            const name = nameInput.value.trim();
            
            if (name.length < 3) {
                e.preventDefault();
                alert('Structure name must be at least 3 characters long.');
                nameInput.focus();
                return false;
            }
            
            if (name.length > 255) {
                e.preventDefault();
                alert('Structure name cannot exceed 255 characters.');
                nameInput.focus();
                return false;
            }
            
            // Show loading state
            const submitBtn = form.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Creating...';
            submitBtn.disabled = true;
        });
        
        // Character counter for name field
        nameInput.addEventListener('input', function() {
            const length = this.value.length;
            const maxLength = 255;
            const remaining = maxLength - length;
            
            let helpText = this.parentNode.querySelector('.form-text');
            if (length > 0) {
                helpText.innerHTML = `Enter a descriptive name for your organizational structure. ${remaining} characters remaining.`;
                if (remaining < 50) {
                    helpText.className = 'form-text text-warning';
                } else {
                    helpText.className = 'form-text';
                }
            }
        });

        console.log('Create Structure form initialized successfully');
    });
</script>
<?= $this->endSection() ?>
