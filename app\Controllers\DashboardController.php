<?php

namespace App\Controllers;

class DashboardController extends BaseController
{
    /**
     * Display the dashboard landing page (GET request)
     *
     * @return string
     */
    public function index()
    {
        // Get user role from session (for demo purposes, we'll simulate this)
        $userRole = $this->getUserRole();

        // Prepare dashboard data based on user role
        $data = [
            'user_role' => $userRole,
            'user_name' => $this->getUserName(),
            'dashboard_stats' => $this->getDashboardStats($userRole),
            'recent_activities' => $this->getRecentActivities($userRole)
        ];

        return view('dashboard/dashboard_landing', $data);
    }

    /**
     * Handle dashboard data requests (GET request)
     * RESTful endpoint for dashboard data
     *
     * @return \CodeIgniter\HTTP\ResponseInterface
     */
    public function getData()
    {
        // This method can be used for AJAX requests to get dashboard data
        $data = [
            'status' => 'success',
            'message' => 'Dashboard data retrieved successfully',
            'data' => [
                'active_workplans' => 24,
                'pending_claims' => 12,
                'total_users' => 156,
                'completion_rate' => 89
            ]
        ];

        return $this->response->setJSON($data);
    }

    /**
     * Handle dashboard form submissions (POST request)
     *
     * @return \CodeIgniter\HTTP\ResponseInterface
     */
    public function store()
    {
        // Handle POST requests for dashboard actions
        $input = $this->request->getJSON(true) ?? $this->request->getPost();

        // Validate input
        if (empty($input)) {
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'No data provided'
            ])->setStatusCode(400);
        }

        // Process the dashboard action based on input
        $action = $input['action'] ?? '';

        switch ($action) {
            case 'update_preferences':
                return $this->updatePreferences($input);
            case 'refresh_stats':
                return $this->refreshStats();
            default:
                return $this->response->setJSON([
                    'status' => 'error',
                    'message' => 'Invalid action specified'
                ])->setStatusCode(400);
        }
    }

    /**
     * Update user dashboard preferences (POST request)
     *
     * @param array $input
     * @return \CodeIgniter\HTTP\ResponseInterface
     */
    private function updatePreferences($input)
    {
        // Handle dashboard preference updates
        $preferences = $input['preferences'] ?? [];

        // Here you would typically save to database
        // For now, return success response

        return $this->response->setJSON([
            'status' => 'success',
            'message' => 'Dashboard preferences updated successfully',
            'data' => $preferences
        ]);
    }

    /**
     * Refresh dashboard statistics (POST request)
     *
     * @return \CodeIgniter\HTTP\ResponseInterface
     */
    private function refreshStats()
    {
        // Refresh and return updated statistics
        $stats = [
            'active_workplans' => rand(20, 30),
            'pending_claims' => rand(10, 20),
            'total_users' => rand(150, 200),
            'completion_rate' => rand(80, 95)
        ];

        return $this->response->setJSON([
            'status' => 'success',
            'message' => 'Dashboard statistics refreshed',
            'data' => $stats
        ]);
    }

    /**
     * Handle dashboard updates (PUT request)
     *
     * @param int $id
     * @return \CodeIgniter\HTTP\ResponseInterface
     */
    public function update($id = null)
    {
        if (!$id) {
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'ID is required for update'
            ])->setStatusCode(400);
        }

        $input = $this->request->getJSON(true) ?? $this->request->getPost();

        // Handle specific dashboard item updates
        return $this->response->setJSON([
            'status' => 'success',
            'message' => "Dashboard item {$id} updated successfully",
            'data' => $input
        ]);
    }

    /**
     * Delete dashboard item (DELETE request)
     *
     * @param int $id
     * @return \CodeIgniter\HTTP\ResponseInterface
     */
    public function delete($id = null)
    {
        if (!$id) {
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'ID is required for deletion'
            ])->setStatusCode(400);
        }

        // Handle dashboard item deletion
        return $this->response->setJSON([
            'status' => 'success',
            'message' => "Dashboard item {$id} deleted successfully"
        ]);
    }

    /**
     * Get user role from session or database
     * For demo purposes, this simulates different roles
     *
     * @return string
     */
    private function getUserRole()
    {
        // In a real implementation, this would get the role from session or database
        // For demo purposes, allow testing different roles via URL parameter
        $testRole = $this->request->getGet('role');
        $validRoles = ['officer', 'group_admin', 'supervisor', 'aro', 'fund_manager', 'administrator'];

        if ($testRole && in_array($testRole, $validRoles)) {
            return $testRole;
        }

        // Default to administrator to show all buttons
        return 'administrator';
    }

    /**
     * Get user name from session or database
     *
     * @return string
     */
    private function getUserName()
    {
        // In a real implementation, this would get the name from session or database
        return 'John Doe';
    }

    /**
     * Get dashboard statistics based on user role
     *
     * @param string $userRole
     * @return array
     */
    private function getDashboardStats($userRole)
    {
        $baseStats = [
            'active_workplans' => 24,
            'pending_claims' => 'K 12,450',
            'total_users' => 156,
            'budget_utilized' => 'K 89,750'
        ];

        // Customize stats based on role
        switch ($userRole) {
            case 'fund_manager':
                $baseStats['pending_approvals'] = 8;
                break;
            case 'supervisor':
                $baseStats['team_workplans'] = 12;
                break;
            case 'administrator':
                $baseStats['system_users'] = 156;
                $baseStats['total_budget'] = 'K 125,000';
                break;
        }

        return $baseStats;
    }

    /**
     * Get recent activities based on user role
     *
     * @param string $userRole
     * @return array
     */
    private function getRecentActivities($userRole)
    {
        $activities = [
            [
                'title' => 'Workplan Approved',
                'description' => 'Q1 Marketing Plan has been approved by supervisor',
                'time' => '2 hours ago',
                'type' => 'success'
            ],
            [
                'title' => 'New User Added',
                'description' => 'John Doe has been added to the system',
                'time' => '4 hours ago',
                'type' => 'info'
            ],
            [
                'title' => 'Budget Alert',
                'description' => 'IT Department budget K 85,000 (85%) utilized',
                'time' => '6 hours ago',
                'type' => 'warning'
            ],
            [
                'title' => 'Report Generated',
                'description' => 'Monthly progress report has been generated',
                'time' => '1 day ago',
                'type' => 'success'
            ],
            [
                'title' => 'Claim Rejected',
                'description' => 'Travel claim #TC-001 (K 2,500) requires additional documentation',
                'time' => '2 days ago',
                'type' => 'danger'
            ]
        ];

        // Filter activities based on role if needed
        return $activities;
    }
}
