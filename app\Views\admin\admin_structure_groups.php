<?= $this->extend('templates/admin_template') ?>

<?= $this->section('title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('page_title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
    .admin-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .breadcrumb-item a {
        color: #800000;
        text-decoration: none;
    }

    .breadcrumb-item a:hover {
        color: #28a745;
    }

    .table th {
        background-color: #f8f9fa;
        border-top: none;
        color: #800000;
        font-weight: 600;
    }

    .btn-action {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
        margin: 0 0.125rem;
    }

    .group-level-0 { padding-left: 0; }
    .group-level-1 { padding-left: 1.5rem; }
    .group-level-2 { padding-left: 3rem; }
    .group-level-3 { padding-left: 4.5rem; }

    .group-hierarchy {
        border-left: 3px solid #e9ecef;
        margin-left: 10px;
        padding-left: 15px;
    }

    .group-card {
        border: 1px solid #e9ecef;
        border-radius: 8px;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .group-card:hover {
        border-color: #800000;
        box-shadow: 0 2px 8px rgba(128, 0, 0, 0.1);
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="fade-in">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <?php foreach ($breadcrumbs as $index => $breadcrumb): ?>
                        <?php if ($index === count($breadcrumbs) - 1): ?>
                            <li class="breadcrumb-item active" aria-current="page"><?= esc($breadcrumb['title']) ?></li>
                        <?php else: ?>
                            <li class="breadcrumb-item">
                                <a href="<?= esc($breadcrumb['url']) ?>"><?= esc($breadcrumb['title']) ?></a>
                            </li>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="admin-card p-4">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h3 class="mb-2 text-success">
                            <i class="fas fa-users me-2"></i>Groups Management
                        </h3>
                        <p class="text-muted mb-1">
                            Structure: <strong><?= esc($structure['name']) ?></strong>
                        </p>
                        <p class="text-muted mb-0 small">
                            Manage groups (departments, divisions, units) within this organizational structure.
                        </p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <div class="btn-group">
                            <a href="<?= site_url('admin/structures') ?>" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to Structures
                            </a>
                            <a href="<?= site_url('admin/structures/' . $structure['id'] . '/groups/create') ?>" class="btn btn-success">
                                <i class="fas fa-plus me-2"></i>Create Group
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <?php if (session()->getFlashdata('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?= session()->getFlashdata('success') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Groups List -->
    <div class="row">
        <div class="col-12">
            <div class="admin-card p-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h5 class="fw-bold mb-0" style="color: #800000;">
                        <i class="fas fa-sitemap me-2"></i>Organizational Groups
                    </h5>
                    <span class="badge bg-info">
                        <?= count($groups) ?> Group<?= count($groups) !== 1 ? 's' : '' ?>
                    </span>
                </div>

                <?php if (empty($groups)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No Groups Found</h5>
                        <p class="text-muted">Create your first group within this structure to get started.</p>
                        <a href="<?= site_url('admin/structures/' . $structure['id'] . '/groups/create') ?>" class="btn btn-success">
                            <i class="fas fa-plus me-2"></i>Create First Group
                        </a>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Group Name</th>
                                    <th>Parent Group</th>
                                    <th>Level</th>
                                    <th>Positions</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($groups as $group): ?>
                                <tr>
                                    <td class="group-level-<?= $group['level'] ?>">
                                        <?php for ($i = 0; $i < $group['level']; $i++): ?>
                                            <i class="fas fa-level-up-alt text-muted me-1" style="transform: rotate(90deg); font-size: 0.8rem;"></i>
                                        <?php endfor; ?>
                                        <strong><?= esc($group['name']) ?></strong>
                                        <?php if ($group['level'] == 0): ?>
                                            <span class="badge bg-primary ms-2">Root Level</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($group['parent_group_id']): ?>
                                            <?php
                                            $parent = array_filter($groups, function($g) use ($group) {
                                                return $g['id'] == $group['parent_group_id'];
                                            });
                                            $parent = reset($parent);
                                            ?>
                                            <span class="text-muted"><?= $parent ? esc($parent['name']) : 'Unknown' ?></span>
                                        <?php else: ?>
                                            <span class="text-muted">None</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">Level <?= $group['level'] ?></span>
                                    </td>
                                    <td>
                                        <a href="<?= site_url('admin/groups/' . $group['id'] . '/positions') ?>" 
                                           class="btn btn-outline-success btn-sm">
                                            <i class="fas fa-user-tie me-1"></i>View Positions
                                        </a>
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            <?= date('M d, Y', strtotime($group['created_at'])) ?>
                                        </small>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="<?= site_url('admin/structures/' . $structure['id'] . '/groups/' . $group['id'] . '/edit') ?>" 
                                               class="btn btn-outline-primary btn-action" title="Edit Group">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <form method="POST" action="<?= site_url('admin/structures/' . $structure['id'] . '/groups/' . $group['id']) ?>" class="d-inline">
                                                <?= csrf_field() ?>
                                                <input type="hidden" name="_method" value="DELETE">
                                                <button type="submit" class="btn btn-outline-danger btn-action" 
                                                        title="Delete Group"
                                                        onclick="return confirm('Are you sure you want to delete this group? This will also delete all positions within this group.')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Navigation Help -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="admin-card p-3">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h6 class="mb-1 text-primary">
                            <i class="fas fa-info-circle me-2"></i>Navigation Flow
                        </h6>
                        <p class="mb-0 text-muted small">
                            Structure → <strong>Groups</strong> → Positions. Click "View Positions" to manage positions within each group.
                        </p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <div class="btn-group">
                            <a href="<?= site_url('admin/structures') ?>" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-arrow-left me-2"></i>Back to Structures
                            </a>
                            <a href="<?= site_url('admin/structures/' . $structure['id'] . '/groups/create') ?>" class="btn btn-success btn-sm">
                                <i class="fas fa-plus me-2"></i>Add Group
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-hide alerts after 5 seconds
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(function(alert) {
            setTimeout(function() {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }, 5000);
        });

        console.log('Groups Management initialized successfully');
    });
</script>
<?= $this->endSection() ?>
