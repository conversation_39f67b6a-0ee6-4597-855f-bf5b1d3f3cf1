<?= $this->extend('templates/admin_template') ?>

<?= $this->section('title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('page_title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
    .admin-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .breadcrumb-item a {
        color: #800000;
        text-decoration: none;
    }

    .breadcrumb-item a:hover {
        color: #28a745;
    }

    .form-label {
        color: #800000;
        font-weight: 600;
    }

    .form-control:focus {
        border-color: #28a745;
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
    }

    .btn-success {
        background-color: #28a745;
        border-color: #28a745;
    }

    .btn-success:hover {
        background-color: #218838;
        border-color: #1e7e34;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="fade-in">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <?php foreach ($breadcrumbs as $index => $breadcrumb): ?>
                        <?php if ($index === count($breadcrumbs) - 1): ?>
                            <li class="breadcrumb-item active" aria-current="page"><?= esc($breadcrumb['title']) ?></li>
                        <?php else: ?>
                            <li class="breadcrumb-item">
                                <a href="<?= esc($breadcrumb['url']) ?>"><?= esc($breadcrumb['title']) ?></a>
                            </li>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="admin-card p-4">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h3 class="mb-2 text-success">
                            <i class="fas fa-edit me-2"></i>Edit Structure
                        </h3>
                        <p class="text-muted mb-0">
                            Update the organizational structure details.
                        </p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <a href="<?= site_url('admin/structures') ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Back to Structures
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Form -->
    <div class="row">
        <div class="col-lg-8 col-md-10 mx-auto">
            <div class="admin-card p-4">
                <form method="POST" action="<?= site_url('admin/structures/' . $structure['id']) ?>" id="editStructureForm">
                    <?= csrf_field() ?>
                    <input type="hidden" name="_method" value="PUT">
                    
                    <div class="mb-4">
                        <label for="name" class="form-label">
                            <i class="fas fa-building me-2"></i>Structure Name *
                        </label>
                        <input type="text" 
                               class="form-control form-control-lg" 
                               id="name" 
                               name="name" 
                               value="<?= esc($structure['name']) ?>"
                               placeholder="Enter structure name"
                               required
                               maxlength="255">
                        <div class="form-text">
                            Enter a descriptive name for your organizational structure.
                        </div>
                    </div>

                    <div class="mb-4">
                        <div class="form-check">
                            <input class="form-check-input" 
                                   type="checkbox" 
                                   id="is_active" 
                                   name="is_active" 
                                   value="1"
                                   <?= $structure['is_active'] ? 'checked' : '' ?>>
                            <label class="form-check-label" for="is_active">
                                <strong>Set as Active Structure</strong>
                            </label>
                        </div>
                        <div class="form-text">
                            Check this box to make this the active organizational structure. Only one structure can be active at a time.
                        </div>
                    </div>

                    <?php if ($structure['is_active']): ?>
                        <div class="mb-4">
                            <div class="alert alert-success">
                                <h6 class="alert-heading">
                                    <i class="fas fa-check-circle me-2"></i>Currently Active Structure
                                </h6>
                                <p class="mb-0">
                                    This structure is currently active and being used throughout the system.
                                </p>
                            </div>
                        </div>
                    <?php endif; ?>

                    <div class="mb-4">
                        <div class="alert alert-info">
                            <h6 class="alert-heading">
                                <i class="fas fa-info-circle me-2"></i>Structure Information
                            </h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <p class="mb-1"><strong>Created:</strong> <?= date('M d, Y \a\t g:i A', strtotime($structure['created_at'])) ?></p>
                                </div>
                                <div class="col-md-6">
                                    <p class="mb-1"><strong>Status:</strong> 
                                        <span class="badge bg-<?= $structure['is_active'] ? 'success' : 'secondary' ?>">
                                            <?= $structure['is_active'] ? 'Active' : 'Inactive' ?>
                                        </span>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex gap-3">
                        <button type="submit" class="btn btn-success btn-lg">
                            <i class="fas fa-save me-2"></i>Update Structure
                        </button>
                        <a href="<?= site_url('admin/structures') ?>" class="btn btn-outline-secondary btn-lg">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                        <a href="<?= site_url('admin/structures/' . $structure['id'] . '/groups') ?>" class="btn btn-outline-info btn-lg">
                            <i class="fas fa-users me-2"></i>Manage Groups
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Help Section -->
    <div class="row mt-4">
        <div class="col-lg-8 col-md-10 mx-auto">
            <div class="admin-card p-3">
                <h6 class="mb-2 text-primary">
                    <i class="fas fa-question-circle me-2"></i>Editing Guidelines
                </h6>
                <div class="row">
                    <div class="col-md-6">
                        <ul class="list-unstyled small text-muted">
                            <li><i class="fas fa-check text-success me-2"></i>Changes take effect immediately</li>
                            <li><i class="fas fa-check text-success me-2"></i>Structure name must be unique</li>
                            <li><i class="fas fa-check text-success me-2"></i>Active status affects entire system</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <ul class="list-unstyled small text-muted">
                            <li><i class="fas fa-exclamation-triangle text-warning me-2"></i>Activating this will deactivate others</li>
                            <li><i class="fas fa-exclamation-triangle text-warning me-2"></i>Consider impact on existing data</li>
                            <li><i class="fas fa-exclamation-triangle text-warning me-2"></i>Test changes in development first</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.getElementById('editStructureForm');
        const nameInput = document.getElementById('name');
        const activeCheckbox = document.getElementById('is_active');
        
        // Form validation
        form.addEventListener('submit', function(e) {
            const name = nameInput.value.trim();
            
            if (name.length < 3) {
                e.preventDefault();
                alert('Structure name must be at least 3 characters long.');
                nameInput.focus();
                return false;
            }
            
            if (name.length > 255) {
                e.preventDefault();
                alert('Structure name cannot exceed 255 characters.');
                nameInput.focus();
                return false;
            }
            
            // Confirm activation change
            if (activeCheckbox.checked && !<?= $structure['is_active'] ? 'true' : 'false' ?>) {
                if (!confirm('Are you sure you want to activate this structure? This will deactivate the currently active structure.')) {
                    e.preventDefault();
                    return false;
                }
            }
            
            // Show loading state
            const submitBtn = form.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Updating...';
            submitBtn.disabled = true;
        });
        
        // Character counter for name field
        nameInput.addEventListener('input', function() {
            const length = this.value.length;
            const maxLength = 255;
            const remaining = maxLength - length;
            
            let helpText = this.parentNode.querySelector('.form-text');
            if (length > 0) {
                helpText.innerHTML = `Enter a descriptive name for your organizational structure. ${remaining} characters remaining.`;
                if (remaining < 50) {
                    helpText.className = 'form-text text-warning';
                } else {
                    helpText.className = 'form-text';
                }
            }
        });

        console.log('Edit Structure form initialized successfully');
    });
</script>
<?= $this->endSection() ?>
