<?= $this->extend('templates/admin_template') ?>

<?= $this->section('title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('page_title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
    .admin-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .admin-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(0,0,0,0.12);
    }

    .breadcrumb-item a {
        color: #800000;
        text-decoration: none;
    }

    .breadcrumb-item a:hover {
        color: #28a745;
    }

    .legislation-card {
        border-left: 4px solid #800000;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .legislation-card:hover {
        border-left-color: #28a745;
        transform: translateY(-3px);
    }

    .activity-card {
        border-left: 4px solid #28a745;
        transition: all 0.3s ease;
    }

    .activity-card:hover {
        border-left-color: #800000;
        transform: translateX(5px);
    }

    .metric-card {
        text-align: center;
        padding: 1.5rem;
        border-radius: 10px;
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    }

    .metric-value {
        font-size: 2rem;
        font-weight: bold;
        color: #800000;
    }

    .metric-label {
        color: #6c757d;
        font-size: 0.875rem;
        margin-top: 0.5rem;
    }

    .priority-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }

    .nav-button {
        background: white;
        border: 2px solid #e9ecef;
        border-radius: 15px;
        padding: 2rem;
        text-align: center;
        transition: all 0.3s ease;
        text-decoration: none;
        color: #343a40;
        display: block;
        height: 100%;
        min-height: 180px;
    }

    .nav-button:hover {
        border-color: #800000;
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        color: #800000;
        text-decoration: none;
    }

    .nav-button i {
        font-size: 3rem;
        color: #28a745;
        margin-bottom: 1rem;
        display: block;
    }

    .nav-button:hover i {
        color: #800000;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="fade-in">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <?php foreach ($breadcrumbs as $index => $breadcrumb): ?>
                        <?php if ($index === count($breadcrumbs) - 1): ?>
                            <li class="breadcrumb-item active" aria-current="page"><?= esc($breadcrumb['title']) ?></li>
                        <?php else: ?>
                            <li class="breadcrumb-item">
                                <a href="<?= esc($breadcrumb['url']) ?>"><?= esc($breadcrumb['title']) ?></a>
                            </li>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="admin-card p-4">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h3 class="mb-2 text-success">
                            <i class="fas fa-gavel me-2"></i>Legislated Activities Management
                        </h3>
                        <p class="text-muted mb-0">
                            Manage legislation and mandatory activities that must be linked to workplans.
                            These activities are pre-listed by administrators and assigned to groups.
                        </p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <a href="<?= site_url('admin') ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Back to Admin
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Overview Statistics -->
    <div class="row g-4 mb-4">
        <div class="col-lg-3 col-md-6">
            <div class="metric-card">
                <div class="metric-value"><?= count($legislation) ?></div>
                <div class="metric-label">Active Legislation</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="metric-card">
                <div class="metric-value"><?= count($activities) ?></div>
                <div class="metric-label">Legislated Activities</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="metric-card">
                <div class="metric-value"><?= count($groups) ?></div>
                <div class="metric-label">Linked Groups</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="metric-card">
                <div class="metric-value">
                    <?= count(array_filter($activities, function($a) { return $a['priority'] === 'Critical'; })) ?>
                </div>
                <div class="metric-label">Critical Activities</div>
            </div>
        </div>
    </div>

    <!-- Navigation Options -->
    <div class="row g-4 mb-4">
        <div class="col-lg-6">
            <a href="<?= site_url('admin/legislation') ?>" class="nav-button">
                <i class="fas fa-balance-scale"></i>
                <h5 class="fw-bold">Manage Legislation</h5>
                <p class="text-muted mb-0">Create and manage legislation documents that define mandatory activities</p>
            </a>
        </div>
        <div class="col-lg-6">
            <div class="nav-button" style="opacity: 0.7; cursor: not-allowed;">
                <i class="fas fa-tasks"></i>
                <h5 class="fw-bold">View All Activities</h5>
                <p class="text-muted mb-0">Browse all legislated activities across all legislation</p>
                <small class="text-warning">Select legislation first</small>
            </div>
        </div>
    </div>

    <!-- Recent Legislation -->
    <div class="row">
        <div class="col-12">
            <div class="admin-card p-4">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="fw-bold mb-0" style="color: #800000;">
                        <i class="fas fa-balance-scale me-2"></i>Recent Legislation
                    </h5>
                    <a href="<?= site_url('admin/legislation') ?>" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-eye me-1"></i>View All
                    </a>
                </div>

                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Legislation</th>
                                <th>Status</th>
                                <th>Date Enacted</th>
                                <th>Reference</th>
                                <th>Activities</th>
                                <th>Description</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach (array_slice($legislation, 0, 4) as $law): ?>
                            <tr>
                                <td>
                                    <strong><?= esc($law['name']) ?></strong>
                                </td>
                                <td>
                                    <span class="badge bg-success"><?= esc($law['status']) ?></span>
                                </td>
                                <td><?= date('M d, Y', strtotime($law['date_enacted'])) ?></td>
                                <td>
                                    <span class="fw-bold text-info"><?= esc($law['reference_number']) ?></span>
                                </td>
                                <td>
                                    <span class="fw-bold text-primary"><?= $law['activities_count'] ?></span>
                                </td>
                                <td>
                                    <small class="text-muted"><?= esc(substr($law['description'], 0, 50)) ?>...</small>
                                </td>
                                <td>
                                    <a href="<?= site_url('admin/legislation/' . $law['id'] . '/activities') ?>" class="btn btn-outline-primary btn-sm" title="View Activities">
                                        <i class="fas fa-tasks"></i>
                                    </a>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activities -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="admin-card p-4">
                <h5 class="fw-bold mb-3" style="color: #800000;">
                    <i class="fas fa-clock me-2"></i>Recent Activities
                </h5>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Activity</th>
                                <th>Priority</th>
                                <th>Frequency</th>
                                <th>Due Period</th>
                                <th>Description</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach (array_slice($activities, 0, 3) as $activity): ?>
                            <tr>
                                <td>
                                    <strong><?= esc($activity['name']) ?></strong>
                                </td>
                                <td>
                                    <span class="badge bg-<?= $activity['priority'] === 'Critical' ? 'danger' : ($activity['priority'] === 'High' ? 'warning' : 'info') ?>">
                                        <?= esc($activity['priority']) ?>
                                    </span>
                                </td>
                                <td><?= esc($activity['frequency']) ?></td>
                                <td>
                                    <span class="text-primary"><?= esc($activity['due_period']) ?></span>
                                </td>
                                <td>
                                    <small class="text-muted"><?= esc($activity['description']) ?></small>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Legislated Activities Management initialized successfully');
    });
</script>
<?= $this->endSection() ?>
