<?php

namespace App\Models;

use CodeIgniter\Model;

class StructureModel extends Model
{
    protected $table            = 'structures';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'name',
        'is_active'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    // Validation
    protected $validationRules = [
        'name' => 'required|min_length[3]|max_length[255]|is_unique[structures.name,id,{id}]'
    ];

    protected $validationMessages = [
        'name' => [
            'required'    => 'Structure name is required.',
            'min_length'  => 'Structure name must be at least 3 characters long.',
            'max_length'  => 'Structure name cannot exceed 255 characters.',
            'is_unique'   => 'This structure name already exists.'
        ]
    ];

    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = [];
    protected $afterInsert    = [];
    protected $beforeUpdate   = [];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];

    /**
     * Get all structures with group counts
     *
     * @return array
     */
    public function getStructuresWithCounts()
    {
        return $this->select('structures.*, 
                             COUNT(groups.id) as groups_count')
                    ->join('groups', 'groups.structure_id = structures.id', 'left')
                    ->groupBy('structures.id')
                    ->orderBy('structures.created_at', 'DESC')
                    ->findAll();
    }

    /**
     * Get active structure
     *
     * @return array|null
     */
    public function getActiveStructure()
    {
        return $this->where('is_active', 1)->first();
    }

    /**
     * Activate a structure and deactivate others
     *
     * @param int $id
     * @return bool
     */
    public function activateStructure($id)
    {
        $db = \Config\Database::connect();
        $db->transStart();

        // Deactivate all structures
        $this->set('is_active', 0)->update();

        // Activate the selected structure
        $result = $this->update($id, ['is_active' => 1]);

        $db->transComplete();

        return $db->transStatus() && $result;
    }

    /**
     * Check if structure can be deleted
     *
     * @param int $id
     * @return bool
     */
    public function canDelete($id)
    {
        $groupModel = new \App\Models\GroupModel();
        $groupsCount = $groupModel->where('structure_id', $id)->countAllResults();
        
        return $groupsCount === 0;
    }
}
