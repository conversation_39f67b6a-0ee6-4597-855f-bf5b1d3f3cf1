<?= $this->extend('templates/admin_template') ?>

<?= $this->section('title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('page_title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
    .admin-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .breadcrumb-item a {
        color: #800000;
        text-decoration: none;
    }

    .breadcrumb-item a:hover {
        color: #28a745;
    }

    .form-label {
        color: #800000;
        font-weight: 600;
    }

    .form-control:focus, .form-select:focus {
        border-color: #28a745;
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
    }

    .btn-success {
        background-color: #28a745;
        border-color: #28a745;
    }

    .btn-success:hover {
        background-color: #218838;
        border-color: #1e7e34;
    }

    .plan-type-info {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .type-option {
        border: 2px solid #e9ecef;
        border-radius: 8px;
        padding: 1rem;
        cursor: pointer;
        transition: all 0.3s ease;
        background-color: white;
    }

    .type-option:hover {
        border-color: #800000;
        background-color: #f8f9fa;
    }

    .type-option.selected {
        border-color: #28a745;
        background-color: #f0f8f0;
    }

    .type-option input[type="radio"] {
        display: none;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="fade-in">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <?php foreach ($breadcrumbs as $index => $breadcrumb): ?>
                        <?php if ($index === count($breadcrumbs) - 1): ?>
                            <li class="breadcrumb-item active" aria-current="page"><?= esc($breadcrumb['title']) ?></li>
                        <?php else: ?>
                            <li class="breadcrumb-item">
                                <a href="<?= esc($breadcrumb['url']) ?>"><?= esc($breadcrumb['title']) ?></a>
                            </li>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="admin-card p-4">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h3 class="mb-2 text-success">
                            <i class="fas fa-plus-circle me-2"></i>Create New Plan
                        </h3>
                        <p class="text-muted mb-0">
                            Create a new strategic plan for your organization.
                        </p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <a href="<?= site_url('admin/plans') ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Back to Plans
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Create Form -->
    <div class="row">
        <div class="col-lg-10 mx-auto">
            <div class="admin-card p-4">
                <form method="POST" action="<?= site_url('admin/plans') ?>" id="createPlanForm">
                    <?= csrf_field() ?>

                    <!-- Plan Type Selection -->
                    <div class="mb-4">
                        <label class="form-label">
                            <i class="fas fa-clipboard-list me-2"></i>Plan Type *
                        </label>
                        <div class="row">
                            <div class="col-md-6">
                                <label class="type-option" for="type_development">
                                    <input type="radio" id="type_development" name="type" value="Development" required>
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-project-diagram fa-2x text-primary me-3"></i>
                                        <div>
                                            <h6 class="mb-1">Development Plan</h6>
                                            <small class="text-muted">Contains Programs → Projects</small>
                                        </div>
                                    </div>
                                </label>
                            </div>
                            <div class="col-md-6">
                                <label class="type-option" for="type_corporate">
                                    <input type="radio" id="type_corporate" name="type" value="Corporate" required>
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-bullseye fa-2x text-warning me-3"></i>
                                        <div>
                                            <h6 class="mb-1">Corporate Plan</h6>
                                            <small class="text-muted">Contains KRAs → KPIs</small>
                                        </div>
                                    </div>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-4">
                                <label for="name" class="form-label">
                                    <i class="fas fa-file-alt me-2"></i>Plan Name *
                                </label>
                                <input type="text"
                                       class="form-control form-control-lg"
                                       id="name"
                                       name="name"
                                       placeholder="Enter plan name (e.g., PNG Vision 2050, Treasury Corporate Plan 2024)"
                                       required
                                       maxlength="255">
                                <div class="form-text text-muted">
                                    Enter a descriptive name for your strategic plan.
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-4">
                                <label for="fiscal_year" class="form-label">
                                    <i class="fas fa-calendar me-2"></i>Fiscal Year *
                                </label>
                                <select class="form-select form-select-lg" id="fiscal_year" name="fiscal_year" required>
                                    <option value="">Select Year</option>
                                    <?php for ($year = 2024; $year <= 2030; $year++): ?>
                                        <option value="<?= $year ?>" <?= $year == 2024 ? 'selected' : '' ?>><?= $year ?></option>
                                    <?php endfor; ?>
                                </select>
                                <div class="form-text text-muted">
                                    Select the fiscal year for this plan.
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-4">
                        <label for="description" class="form-label">
                            <i class="fas fa-align-left me-2"></i>Description
                        </label>
                        <textarea class="form-control"
                                  id="description"
                                  name="description"
                                  rows="4"
                                  placeholder="Enter a detailed description of the plan's objectives and scope"
                                  maxlength="1000"></textarea>
                        <div class="form-text text-muted">
                            Provide a comprehensive description of the plan's purpose and objectives.
                        </div>
                    </div>

                    <div class="mb-4">
                        <label for="status" class="form-label">
                            <i class="fas fa-toggle-on me-2"></i>Status
                        </label>
                        <select class="form-select" id="status" name="status" required>
                            <option value="Draft">Draft</option>
                            <option value="Active" selected>Active</option>
                            <option value="Completed">Completed</option>
                            <option value="Suspended">Suspended</option>
                        </select>
                        <div class="form-text text-muted">
                            Set the initial status of the plan.
                        </div>
                    </div>

                    <!-- Plan Type Information -->
                    <div class="plan-type-info" id="planTypeInfo" style="display: none;">
                        <h6 class="text-primary mb-3">
                            <i class="fas fa-info-circle me-2"></i>Plan Structure
                        </h6>
                        <div id="developmentInfo" style="display: none;">
                            <p class="text-muted mb-2">
                                <strong>Development Plans</strong> follow this hierarchy:
                            </p>
                            <ul class="text-muted mb-0">
                                <li><strong>Programs:</strong> Major development initiatives</li>
                                <li><strong>Projects:</strong> Specific implementation activities within programs</li>
                                <li>Examples: Vision 2050, MTDP IV, Infrastructure Development Plans</li>
                            </ul>
                        </div>
                        <div id="corporateInfo" style="display: none;">
                            <p class="text-muted mb-2">
                                <strong>Corporate Plans</strong> follow this hierarchy:
                            </p>
                            <ul class="text-muted mb-0">
                                <li><strong>KRAs (Key Result Areas):</strong> Strategic focus areas</li>
                                <li><strong>KPIs (Key Performance Indicators):</strong> Measurable performance metrics</li>
                                <li>Examples: Department Strategic Plans, Ministry Corporate Plans</li>
                            </ul>
                        </div>
                    </div>

                    <div class="d-flex gap-3">
                        <button type="submit" class="btn btn-success btn-lg">
                            <i class="fas fa-save me-2"></i>Create Plan
                        </button>
                        <a href="<?= site_url('admin/plans') ?>" class="btn btn-outline-secondary btn-lg">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.getElementById('createPlanForm');
        const nameInput = document.getElementById('name');
        const typeRadios = document.querySelectorAll('input[name="type"]');
        const typeOptions = document.querySelectorAll('.type-option');
        const planTypeInfo = document.getElementById('planTypeInfo');
        const developmentInfo = document.getElementById('developmentInfo');
        const corporateInfo = document.getElementById('corporateInfo');

        // Handle type selection
        typeRadios.forEach(radio => {
            radio.addEventListener('change', function() {
                // Update visual selection
                typeOptions.forEach(option => option.classList.remove('selected'));
                this.closest('.type-option').classList.add('selected');

                // Show appropriate info
                planTypeInfo.style.display = 'block';
                if (this.value === 'Development') {
                    developmentInfo.style.display = 'block';
                    corporateInfo.style.display = 'none';
                } else {
                    developmentInfo.style.display = 'none';
                    corporateInfo.style.display = 'block';
                }
            });
        });

        // Handle type option clicks
        typeOptions.forEach(option => {
            option.addEventListener('click', function() {
                const radio = this.querySelector('input[type="radio"]');
                radio.checked = true;
                radio.dispatchEvent(new Event('change'));
            });
        });

        // Form validation
        form.addEventListener('submit', function(e) {
            const name = nameInput.value.trim();
            const selectedType = document.querySelector('input[name="type"]:checked');

            if (!selectedType) {
                e.preventDefault();
                alert('Please select a plan type.');
                return false;
            }

            if (name.length < 3) {
                e.preventDefault();
                alert('Plan name must be at least 3 characters long.');
                nameInput.focus();
                return false;
            }

            // Show loading state
            const submitBtn = form.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Creating...';
            submitBtn.disabled = true;
        });

        // Character counter for name field
        nameInput.addEventListener('input', function() {
            const length = this.value.length;
            const maxLength = 255;
            const remaining = maxLength - length;

            let helpText = this.parentNode.querySelector('.form-text');
            if (length > 0) {
                helpText.innerHTML = `Enter a descriptive name for your strategic plan. ${remaining} characters remaining.`;
                if (remaining < 50) {
                    helpText.className = 'form-text text-warning';
                } else {
                    helpText.className = 'form-text text-muted';
                }
            }
        });

        console.log('Create Plan form initialized successfully');
    });
</script>
<?= $this->endSection() ?>
