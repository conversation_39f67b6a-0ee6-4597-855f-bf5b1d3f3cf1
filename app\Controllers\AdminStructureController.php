<?php

namespace App\Controllers;

class AdminStructureController extends BaseController
{
    /**
     * Display structures list (GET request)
     *
     * @return string
     */
    public function index()
    {
        $data = [
            'page_title' => 'Structure Management',
            'breadcrumbs' => [
                ['title' => 'Dashboard', 'url' => site_url('dashboard')],
                ['title' => 'Administrator Settings', 'url' => site_url('admin')],
                ['title' => 'Structure Management', 'url' => '']
            ],
            'structures' => $this->getDummyStructures()
        ];

        return view('admin/admin_structure_index', $data);
    }

    /**
     * Show create structure form (GET request)
     *
     * @return string
     */
    public function create()
    {
        $data = [
            'page_title' => 'Create Structure',
            'breadcrumbs' => [
                ['title' => 'Dashboard', 'url' => site_url('dashboard')],
                ['title' => 'Administrator Settings', 'url' => site_url('admin')],
                ['title' => 'Structure Management', 'url' => site_url('admin/structures')],
                ['title' => 'Create Structure', 'url' => '']
            ]
        ];

        return view('admin/admin_structure_create', $data);
    }

    /**
     * Store new structure (POST request)
     *
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function store()
    {
        // In real implementation, this would save to database
        // For now, just redirect with success message

        return redirect()->to(site_url('admin/structures'))
                        ->with('success', 'Structure created successfully!');
    }

    /**
     * Show edit structure form (GET request)
     *
     * @param int $id
     * @return string
     */
    public function edit($id)
    {
        $structures = $this->getDummyStructures();
        $structure = array_filter($structures, function($s) use ($id) {
            return $s['id'] == $id;
        });
        $structure = reset($structure);

        if (!$structure) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Structure not found');
        }

        $data = [
            'page_title' => 'Edit Structure',
            'breadcrumbs' => [
                ['title' => 'Dashboard', 'url' => site_url('dashboard')],
                ['title' => 'Administrator Settings', 'url' => site_url('admin')],
                ['title' => 'Structure Management', 'url' => site_url('admin/structures')],
                ['title' => 'Edit Structure', 'url' => '']
            ],
            'structure' => $structure
        ];

        return view('admin/admin_structure_edit', $data);
    }

    /**
     * Update structure (PUT request)
     *
     * @param int $id
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function update($id)
    {
        // In real implementation, this would update the database
        // For now, just redirect with success message

        return redirect()->to(site_url('admin/structures'))
                        ->with('success', 'Structure updated successfully!');
    }

    /**
     * Activate structure (POST request)
     *
     * @param int $id
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function activate($id)
    {
        // In real implementation, this would activate the structure in database
        // For now, just redirect with success message

        return redirect()->to(site_url('admin/structures'))
                        ->with('success', 'Structure activated successfully!');
    }

    /**
     * Delete structure (DELETE request)
     *
     * @param int $id
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function delete($id)
    {
        // In real implementation, this would delete from database
        // For now, just redirect with success message

        return redirect()->to(site_url('admin/structures'))
                        ->with('success', 'Structure deleted successfully!');
    }

    /**
     * Display groups for a specific structure (GET request)
     *
     * @param int $structureId
     * @return string
     */
    public function groups($structureId)
    {
        $structures = $this->getDummyStructures();
        $structure = array_filter($structures, function($s) use ($structureId) {
            return $s['id'] == $structureId;
        });
        $structure = reset($structure);

        if (!$structure) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Structure not found');
        }

        $data = [
            'page_title' => 'Groups - ' . $structure['name'],
            'breadcrumbs' => [
                ['title' => 'Dashboard', 'url' => site_url('dashboard')],
                ['title' => 'Administrator Settings', 'url' => site_url('admin')],
                ['title' => 'Structure Management', 'url' => site_url('admin/structures')],
                ['title' => $structure['name'], 'url' => '']
            ],
            'structure' => $structure,
            'groups' => $this->getDummyGroups($structureId)
        ];

        return view('admin/admin_structure_groups', $data);
    }

    /**
     * Show create group form (GET request)
     *
     * @param int $structureId
     * @return string
     */
    public function createGroup($structureId)
    {
        $structures = $this->getDummyStructures();
        $structure = array_filter($structures, function($s) use ($structureId) {
            return $s['id'] == $structureId;
        });
        $structure = reset($structure);

        if (!$structure) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Structure not found');
        }

        $data = [
            'page_title' => 'Create Group',
            'breadcrumbs' => [
                ['title' => 'Dashboard', 'url' => site_url('dashboard')],
                ['title' => 'Administrator Settings', 'url' => site_url('admin')],
                ['title' => 'Structure Management', 'url' => site_url('admin/structures')],
                ['title' => $structure['name'], 'url' => site_url('admin/structures/' . $structureId . '/groups')],
                ['title' => 'Create Group', 'url' => '']
            ],
            'structure' => $structure,
            'parent_groups' => $this->getDummyGroups($structureId)
        ];

        return view('admin/admin_structure_group_create', $data);
    }

    /**
     * Store new group (POST request)
     *
     * @param int $structureId
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function storeGroup($structureId)
    {
        // In real implementation, this would save to database
        // For now, just redirect with success message

        return redirect()->to(site_url('admin/structures/' . $structureId . '/groups'))
                        ->with('success', 'Group created successfully!');
    }

    /**
     * Show edit group form (GET request)
     *
     * @param int $structureId
     * @param int $groupId
     * @return string
     */
    public function editGroup($structureId, $groupId)
    {
        $structures = $this->getDummyStructures();
        $structure = array_filter($structures, function($s) use ($structureId) {
            return $s['id'] == $structureId;
        });
        $structure = reset($structure);

        $groups = $this->getDummyGroups($structureId);
        $group = array_filter($groups, function($g) use ($groupId) {
            return $g['id'] == $groupId;
        });
        $group = reset($group);

        if (!$structure || !$group) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Structure or Group not found');
        }

        $data = [
            'page_title' => 'Edit Group',
            'breadcrumbs' => [
                ['title' => 'Dashboard', 'url' => site_url('dashboard')],
                ['title' => 'Administrator Settings', 'url' => site_url('admin')],
                ['title' => 'Structure Management', 'url' => site_url('admin/structures')],
                ['title' => $structure['name'], 'url' => site_url('admin/structures/' . $structureId . '/groups')],
                ['title' => 'Edit Group', 'url' => '']
            ],
            'structure' => $structure,
            'group' => $group,
            'parent_groups' => $this->getDummyGroups($structureId)
        ];

        return view('admin/admin_structure_group_edit', $data);
    }

    /**
     * Update group (PUT request)
     *
     * @param int $structureId
     * @param int $groupId
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function updateGroup($structureId, $groupId)
    {
        // In real implementation, this would update the database
        // For now, just redirect with success message

        return redirect()->to(site_url('admin/structures/' . $structureId . '/groups'))
                        ->with('success', 'Group updated successfully!');
    }

    /**
     * Delete group (DELETE request)
     *
     * @param int $structureId
     * @param int $groupId
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function deleteGroup($structureId, $groupId)
    {
        // In real implementation, this would delete from database
        // For now, just redirect with success message

        return redirect()->to(site_url('admin/structures/' . $structureId . '/groups'))
                        ->with('success', 'Group deleted successfully!');
    }

    /**
     * Display positions for a specific group (GET request)
     *
     * @param int $groupId
     * @return string
     */
    public function positions($groupId)
    {
        $groups = $this->getAllDummyGroups();
        $group = array_filter($groups, function($g) use ($groupId) {
            return $g['id'] == $groupId;
        });
        $group = reset($group);

        if (!$group) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Group not found');
        }

        $structures = $this->getDummyStructures();
        $structure = array_filter($structures, function($s) use ($group) {
            return $s['id'] == $group['structure_id'];
        });
        $structure = reset($structure);

        $data = [
            'page_title' => 'Positions - ' . $group['name'],
            'breadcrumbs' => [
                ['title' => 'Dashboard', 'url' => site_url('dashboard')],
                ['title' => 'Administrator Settings', 'url' => site_url('admin')],
                ['title' => 'Structure Management', 'url' => site_url('admin/structures')],
                ['title' => $structure['name'], 'url' => site_url('admin/structures/' . $group['structure_id'] . '/groups')],
                ['title' => $group['name'], 'url' => '']
            ],
            'structure' => $structure,
            'group' => $group,
            'positions' => $this->getDummyPositions($groupId)
        ];

        return view('admin/admin_structure_positions', $data);
    }

    /**
     * Show create position form (GET request)
     *
     * @param int $groupId
     * @return string
     */
    public function createPosition($groupId)
    {
        $groups = $this->getAllDummyGroups();
        $group = array_filter($groups, function($g) use ($groupId) {
            return $g['id'] == $groupId;
        });
        $group = reset($group);

        if (!$group) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Group not found');
        }

        $structures = $this->getDummyStructures();
        $structure = array_filter($structures, function($s) use ($group) {
            return $s['id'] == $group['structure_id'];
        });
        $structure = reset($structure);

        $data = [
            'page_title' => 'Create Position',
            'breadcrumbs' => [
                ['title' => 'Dashboard', 'url' => site_url('dashboard')],
                ['title' => 'Administrator Settings', 'url' => site_url('admin')],
                ['title' => 'Structure Management', 'url' => site_url('admin/structures')],
                ['title' => $structure['name'], 'url' => site_url('admin/structures/' . $group['structure_id'] . '/groups')],
                ['title' => $group['name'], 'url' => site_url('admin/groups/' . $groupId . '/positions')],
                ['title' => 'Create Position', 'url' => '']
            ],
            'structure' => $structure,
            'group' => $group,
            'reporting_positions' => $this->getDummyPositions($groupId)
        ];

        return view('admin/admin_structure_position_create', $data);
    }

    /**
     * Store new position (POST request)
     *
     * @param int $groupId
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function storePosition($groupId)
    {
        // In real implementation, this would save to database
        // For now, just redirect with success message

        return redirect()->to(site_url('admin/groups/' . $groupId . '/positions'))
                        ->with('success', 'Position created successfully!');
    }

    /**
     * Show edit position form (GET request)
     *
     * @param int $groupId
     * @param int $positionId
     * @return string
     */
    public function editPosition($groupId, $positionId)
    {
        $groups = $this->getAllDummyGroups();
        $group = array_filter($groups, function($g) use ($groupId) {
            return $g['id'] == $groupId;
        });
        $group = reset($group);

        $positions = $this->getDummyPositions($groupId);
        $position = array_filter($positions, function($p) use ($positionId) {
            return $p['id'] == $positionId;
        });
        $position = reset($position);

        if (!$group || !$position) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Group or Position not found');
        }

        $structures = $this->getDummyStructures();
        $structure = array_filter($structures, function($s) use ($group) {
            return $s['id'] == $group['structure_id'];
        });
        $structure = reset($structure);

        $data = [
            'page_title' => 'Edit Position',
            'breadcrumbs' => [
                ['title' => 'Dashboard', 'url' => site_url('dashboard')],
                ['title' => 'Administrator Settings', 'url' => site_url('admin')],
                ['title' => 'Structure Management', 'url' => site_url('admin/structures')],
                ['title' => $structure['name'], 'url' => site_url('admin/structures/' . $group['structure_id'] . '/groups')],
                ['title' => $group['name'], 'url' => site_url('admin/groups/' . $groupId . '/positions')],
                ['title' => 'Edit Position', 'url' => '']
            ],
            'structure' => $structure,
            'group' => $group,
            'position' => $position,
            'reporting_positions' => $this->getDummyPositions($groupId)
        ];

        return view('admin/admin_structure_position_edit', $data);
    }

    /**
     * Update position (PUT request)
     *
     * @param int $groupId
     * @param int $positionId
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function updatePosition($groupId, $positionId)
    {
        // In real implementation, this would update the database
        // For now, just redirect with success message

        return redirect()->to(site_url('admin/groups/' . $groupId . '/positions'))
                        ->with('success', 'Position updated successfully!');
    }

    /**
     * Delete position (DELETE request)
     *
     * @param int $groupId
     * @param int $positionId
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function deletePosition($groupId, $positionId)
    {
        // In real implementation, this would delete from database
        // For now, just redirect with success message

        return redirect()->to(site_url('admin/groups/' . $groupId . '/positions'))
                        ->with('success', 'Position deleted successfully!');
    }

    // ========================================
    // DUMMY DATA METHODS
    // ========================================

    /**
     * Get dummy structures data
     *
     * @return array
     */
    private function getDummyStructures()
    {
        return [
            [
                'id' => 1,
                'name' => 'Papua New Guinea Government Structure',
                'is_active' => 1,
                'created_at' => '2024-01-15 10:30:00'
            ],
            [
                'id' => 2,
                'name' => 'Department of Treasury Structure',
                'is_active' => 0,
                'created_at' => '2024-01-20 14:15:00'
            ],
            [
                'id' => 3,
                'name' => 'Provincial Government Structure',
                'is_active' => 0,
                'created_at' => '2024-02-01 09:45:00'
            ],
            [
                'id' => 4,
                'name' => 'Local Level Government Structure',
                'is_active' => 0,
                'created_at' => '2024-02-10 16:20:00'
            ]
        ];
    }

    /**
     * Get dummy groups data for a specific structure
     *
     * @param int $structureId
     * @return array
     */
    private function getDummyGroups($structureId)
    {
        $allGroups = $this->getAllDummyGroups();

        return array_filter($allGroups, function($group) use ($structureId) {
            return $group['structure_id'] == $structureId;
        });
    }

    /**
     * Get all dummy groups data
     *
     * @return array
     */
    private function getAllDummyGroups()
    {
        return [
            // Structure 1 - PNG Government Structure
            [
                'id' => 1,
                'structure_id' => 1,
                'parent_group_id' => null,
                'name' => 'Office of the Prime Minister',
                'level' => 0,
                'created_at' => '2024-01-15 11:00:00'
            ],
            [
                'id' => 2,
                'structure_id' => 1,
                'parent_group_id' => 1,
                'name' => 'Policy and Strategic Planning Division',
                'level' => 1,
                'created_at' => '2024-01-15 11:15:00'
            ],
            [
                'id' => 3,
                'structure_id' => 1,
                'parent_group_id' => 1,
                'name' => 'Cabinet and Parliamentary Services',
                'level' => 1,
                'created_at' => '2024-01-15 11:30:00'
            ],
            [
                'id' => 4,
                'structure_id' => 1,
                'parent_group_id' => null,
                'name' => 'Department of Treasury',
                'level' => 0,
                'created_at' => '2024-01-15 12:00:00'
            ],
            [
                'id' => 5,
                'structure_id' => 1,
                'parent_group_id' => 4,
                'name' => 'Budget Division',
                'level' => 1,
                'created_at' => '2024-01-15 12:15:00'
            ],
            [
                'id' => 6,
                'structure_id' => 1,
                'parent_group_id' => 4,
                'name' => 'Economic Policy Division',
                'level' => 1,
                'created_at' => '2024-01-15 12:30:00'
            ],
            [
                'id' => 7,
                'structure_id' => 1,
                'parent_group_id' => 5,
                'name' => 'Budget Analysis Unit',
                'level' => 2,
                'created_at' => '2024-01-15 12:45:00'
            ],
            // Structure 2 - Department of Treasury Structure
            [
                'id' => 8,
                'structure_id' => 2,
                'parent_group_id' => null,
                'name' => 'Secretary Office',
                'level' => 0,
                'created_at' => '2024-01-20 14:30:00'
            ],
            [
                'id' => 9,
                'structure_id' => 2,
                'parent_group_id' => 8,
                'name' => 'Finance and Administration',
                'level' => 1,
                'created_at' => '2024-01-20 14:45:00'
            ]
        ];
    }

    /**
     * Get dummy positions data for a specific group
     *
     * @param int $groupId
     * @return array
     */
    private function getDummyPositions($groupId)
    {
        $allPositions = [
            // Group 1 - Office of the Prime Minister
            [
                'id' => 1,
                'group_id' => 1,
                'position_no' => 'PM-001',
                'position_name' => 'Prime Minister',
                'grade' => 'PM',
                'reporting_to_id' => null,
                'position_type' => 'Public Servant',
                'is_fund_manager' => 1,
                'is_supervisor' => 1,
                'is_group_admin' => 1,
                'created_at' => '2024-01-15 11:05:00'
            ],
            [
                'id' => 2,
                'group_id' => 1,
                'position_no' => 'PM-002',
                'position_name' => 'Chief of Staff',
                'grade' => 'ES-10',
                'reporting_to_id' => 1,
                'position_type' => 'Public Servant',
                'is_fund_manager' => 0,
                'is_supervisor' => 1,
                'is_group_admin' => 1,
                'created_at' => '2024-01-15 11:10:00'
            ],
            // Group 2 - Policy and Strategic Planning Division
            [
                'id' => 3,
                'group_id' => 2,
                'position_no' => 'PSP-001',
                'position_name' => 'Director Policy and Strategic Planning',
                'grade' => 'ES-9',
                'reporting_to_id' => 2,
                'position_type' => 'Public Servant',
                'is_fund_manager' => 1,
                'is_supervisor' => 1,
                'is_group_admin' => 1,
                'created_at' => '2024-01-15 11:20:00'
            ],
            [
                'id' => 4,
                'group_id' => 2,
                'position_no' => 'PSP-002',
                'position_name' => 'Senior Policy Analyst',
                'grade' => 'ES-7',
                'reporting_to_id' => 3,
                'position_type' => 'Public Servant',
                'is_fund_manager' => 0,
                'is_supervisor' => 1,
                'is_group_admin' => 0,
                'created_at' => '2024-01-15 11:25:00'
            ],
            [
                'id' => 5,
                'group_id' => 2,
                'position_no' => 'PSP-003',
                'position_name' => 'Policy Analyst',
                'grade' => 'ES-5',
                'reporting_to_id' => 4,
                'position_type' => 'Public Servant',
                'is_fund_manager' => 0,
                'is_supervisor' => 0,
                'is_group_admin' => 0,
                'created_at' => '2024-01-15 11:30:00'
            ],
            // Group 4 - Department of Treasury
            [
                'id' => 6,
                'group_id' => 4,
                'position_no' => 'DOT-001',
                'position_name' => 'Secretary for Treasury',
                'grade' => 'ES-10',
                'reporting_to_id' => 1,
                'position_type' => 'Public Servant',
                'is_fund_manager' => 1,
                'is_supervisor' => 1,
                'is_group_admin' => 1,
                'created_at' => '2024-01-15 12:05:00'
            ],
            [
                'id' => 7,
                'group_id' => 4,
                'position_no' => 'DOT-002',
                'position_name' => 'Deputy Secretary',
                'grade' => 'ES-9',
                'reporting_to_id' => 6,
                'position_type' => 'Public Servant',
                'is_fund_manager' => 1,
                'is_supervisor' => 1,
                'is_group_admin' => 0,
                'created_at' => '2024-01-15 12:10:00'
            ],
            // Group 5 - Budget Division
            [
                'id' => 8,
                'group_id' => 5,
                'position_no' => 'BD-001',
                'position_name' => 'Director Budget Division',
                'grade' => 'ES-8',
                'reporting_to_id' => 7,
                'position_type' => 'Public Servant',
                'is_fund_manager' => 1,
                'is_supervisor' => 1,
                'is_group_admin' => 1,
                'created_at' => '2024-01-15 12:20:00'
            ],
            [
                'id' => 9,
                'group_id' => 5,
                'position_no' => 'BD-002',
                'position_name' => 'Senior Budget Analyst',
                'grade' => 'ES-6',
                'reporting_to_id' => 8,
                'position_type' => 'Public Servant',
                'is_fund_manager' => 0,
                'is_supervisor' => 1,
                'is_group_admin' => 0,
                'created_at' => '2024-01-15 12:25:00'
            ],
            [
                'id' => 10,
                'group_id' => 5,
                'position_no' => 'BD-003',
                'position_name' => 'Budget Analyst',
                'grade' => 'ES-4',
                'reporting_to_id' => 9,
                'position_type' => 'Public Servant',
                'is_fund_manager' => 0,
                'is_supervisor' => 0,
                'is_group_admin' => 0,
                'created_at' => '2024-01-15 12:30:00'
            ],
            [
                'id' => 11,
                'group_id' => 5,
                'position_no' => 'BD-004',
                'position_name' => 'Budget Officer',
                'grade' => 'ES-2',
                'reporting_to_id' => 10,
                'position_type' => 'Casual',
                'is_fund_manager' => 0,
                'is_supervisor' => 0,
                'is_group_admin' => 0,
                'created_at' => '2024-01-15 12:35:00'
            ]
        ];

        return array_filter($allPositions, function($position) use ($groupId) {
            return $position['group_id'] == $groupId;
        });
    }
}