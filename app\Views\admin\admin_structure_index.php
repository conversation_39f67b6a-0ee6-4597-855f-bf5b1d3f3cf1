<?= $this->extend('templates/admin_template') ?>

<?= $this->section('title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('page_title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
    .admin-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .admin-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(0,0,0,0.12);
    }

    .breadcrumb-item a {
        color: #800000;
        text-decoration: none;
    }

    .breadcrumb-item a:hover {
        color: #28a745;
    }

    .table th {
        background-color: #f8f9fa;
        border-top: none;
        color: #800000;
        font-weight: 600;
    }

    .btn-action {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
        margin: 0 0.125rem;
    }

    .status-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }

    .structure-card {
        border: 1px solid #e9ecef;
        border-radius: 10px;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .structure-card:hover {
        border-color: #800000;
        box-shadow: 0 4px 12px rgba(128, 0, 0, 0.1);
    }

    .structure-card.active {
        border-color: #28a745;
        background-color: #f8fff9;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="fade-in">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <?php foreach ($breadcrumbs as $index => $breadcrumb): ?>
                        <?php if ($index === count($breadcrumbs) - 1): ?>
                            <li class="breadcrumb-item active" aria-current="page"><?= esc($breadcrumb['title']) ?></li>
                        <?php else: ?>
                            <li class="breadcrumb-item">
                                <a href="<?= esc($breadcrumb['url']) ?>"><?= esc($breadcrumb['title']) ?></a>
                            </li>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="admin-card p-4">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h3 class="mb-2 text-success">
                            <i class="fas fa-sitemap me-2"></i>Structure Management
                        </h3>
                        <p class="text-muted mb-0">
                            Manage organizational structures, groups, and positions. Create hierarchical organizational charts.
                        </p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <div class="btn-group">
                            <a href="<?= site_url('admin') ?>" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to Admin
                            </a>
                            <a href="<?= site_url('admin/structures/create') ?>" class="btn btn-success">
                                <i class="fas fa-plus me-2"></i>Create Structure
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <?php if (session()->getFlashdata('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?= session()->getFlashdata('success') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?= session()->getFlashdata('error') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Structures List -->
    <div class="row">
        <div class="col-12">
            <div class="admin-card p-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h5 class="fw-bold mb-0" style="color: #800000;">
                        <i class="fas fa-building me-2"></i>Organizational Structures
                    </h5>
                    <span class="badge bg-info">
                        <?= count($structures) ?> Structure<?= count($structures) !== 1 ? 's' : '' ?>
                    </span>
                </div>

                <?php if (empty($structures)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-sitemap fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No Structures Found</h5>
                        <p class="text-muted">Create your first organizational structure to get started.</p>
                        <a href="<?= site_url('admin/structures/create') ?>" class="btn btn-success">
                            <i class="fas fa-plus me-2"></i>Create First Structure
                        </a>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Structure Name</th>
                                    <th>Status</th>
                                    <th>Created Date</th>
                                    <th>Groups</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($structures as $structure): ?>
                                <tr class="<?= $structure['is_active'] ? 'table-success' : '' ?>">
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <?php if ($structure['is_active']): ?>
                                                <i class="fas fa-check-circle text-success me-2"></i>
                                            <?php endif; ?>
                                            <div>
                                                <strong><?= esc($structure['name']) ?></strong>
                                                <?php if ($structure['is_active']): ?>
                                                    <br><small class="text-success">Active Structure</small>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <?php if ($structure['is_active']): ?>
                                            <span class="badge bg-success status-badge">Active</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary status-badge">Inactive</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?= date('M d, Y', strtotime($structure['created_at'])) ?></td>
                                    <td>
                                        <a href="<?= site_url('admin/structures/' . $structure['id'] . '/groups') ?>" 
                                           class="btn btn-outline-info btn-sm">
                                            <i class="fas fa-users me-1"></i>View Groups
                                        </a>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <?php if (!$structure['is_active']): ?>
                                                <form method="POST" action="<?= site_url('admin/structures/' . $structure['id'] . '/activate') ?>" class="d-inline">
                                                    <?= csrf_field() ?>
                                                    <button type="submit" class="btn btn-outline-success btn-action" 
                                                            title="Activate Structure"
                                                            onclick="return confirm('Are you sure you want to activate this structure? This will deactivate the current active structure.')">
                                                        <i class="fas fa-check"></i>
                                                    </button>
                                                </form>
                                            <?php endif; ?>
                                            <a href="<?= site_url('admin/structures/' . $structure['id'] . '/edit') ?>" 
                                               class="btn btn-outline-primary btn-action" title="Edit Structure">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <form method="POST" action="<?= site_url('admin/structures/' . $structure['id']) ?>" class="d-inline">
                                                <?= csrf_field() ?>
                                                <input type="hidden" name="_method" value="DELETE">
                                                <button type="submit" class="btn btn-outline-danger btn-action" 
                                                        title="Delete Structure"
                                                        onclick="return confirm('Are you sure you want to delete this structure? This action cannot be undone.')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Navigation Help -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="admin-card p-3">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h6 class="mb-1 text-primary">
                            <i class="fas fa-info-circle me-2"></i>Navigation Flow
                        </h6>
                        <p class="mb-0 text-muted small">
                            Structure → Groups → Positions. Click "View Groups" to manage groups within a structure, then navigate to positions within each group.
                        </p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <a href="<?= site_url('admin') ?>" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-arrow-left me-2"></i>Back to Admin Settings
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-hide alerts after 5 seconds
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(function(alert) {
            setTimeout(function() {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }, 5000);
        });

        console.log('Structure Management initialized successfully');
    });
</script>
<?= $this->endSection() ?>
