# Administrator Settings Module Documentation

## Overview
The Administrator Settings module provides comprehensive administrative functionality for the IPMS system. It implements a mobile app-like interface with button-driven navigation as specified in the UI design writeup.

## Module Structure

### Controller: `AdminController.php`
- **Location**: `app/Controllers/AdminController.php`
- **Architecture**: RESTful approach with separate methods for GET, POST, PUT, DELETE operations
- **Features**:
  - Comprehensive dummy data for demonstration
  - Role-based access control ready
  - Proper error handling and validation
  - Papua New Guinea Kina currency formatting

### Views: `app/Views/admin/` folder
All view files follow the naming convention with `admin_` prefix:

1. **admin_index.php** - Main administrator dashboard with sub-module navigation
2. **admin_users.php** - Users management list view
3. **admin_users_create.php** - Create new user form
4. **admin_users_edit.php** - Edit existing user form
5. **admin_structures.php** - Structure management with tabs for structures, groups, and positions
6. **admin_appointments.php** - Appointments management interface
7. **admin_plans.php** - Plans management for corporate and development plans
8. **admin_budget_books.php** - Budget book and codes management
9. **admin_legislated_activities.php** - Main legislated activities dashboard
10. **admin_legislation.php** - Legislation management list
11. **admin_legislation_create.php** - Create legislation form
12. **admin_activities.php** - Activities within specific legislation
13. **admin_activities_create.php** - Create activity form

### Routes: `app/Config/Routes.php`
RESTful routing structure under `/admin` group:
- GET `/admin` - Main dashboard
- GET/POST `/admin/users` - Users CRUD operations
- GET/POST `/admin/structures` - Structure management
- GET/POST `/admin/appointments` - Appointments management
- GET `/admin/plans` - Plans management
- GET `/admin/budget-books` - Budget management

## Sub-Modules

### 1. Users Management
**Purpose**: Create, edit, and manage system users

**Features**:
- User profile management with photo upload
- Contact details and employment information
- File number assignment
- Password management
- Search and filter functionality
- Bulk operations support

**Dummy Data Fields**:
- Full name, email, file number
- Gender, date of birth, date joined
- Contact details, remarks
- Profile photo support

### 2. Structure Management
**Purpose**: Manage organizational structures, groups, and positions

**Features**:
- Hierarchical organizational structure creation
- Group management with parent-child relationships
- Position definitions with roles and permissions
- Visual hierarchy display
- Tabbed interface for different components

**Components**:
- **Structures**: Top-level organizational frameworks
- **Groups**: Departments and units within structures
- **Positions**: Individual roles with specific permissions

### 3. Appointments Management
**Purpose**: Assign employees to positions within the organizational structure

**Features**:
- Employee-position assignment
- Start and end date management
- Appointment status tracking
- Duration calculations
- Historical appointment records
- Quick statistics dashboard

**Key Metrics**:
- Active appointments count
- Available positions
- Pending appointments
- User assignment status

### 4. Plans Management
**Purpose**: Manage corporate and development plans

**Features**:
- Corporate plan creation and management
- Development plan oversight
- KRA (Key Result Areas) management
- KPI (Key Performance Indicators) tracking
- Program and project management
- Fiscal year planning

**Plan Types**:
- **Corporate Plans**: KRAs and KPIs focused
- **Development Plans**: Programs and projects focused

### 5. Budget Book Management
**Purpose**: Manage budget books and revenue/expenditure codes

**Features**:
- Budget book creation by fiscal year
- Revenue and expenditure code management
- Group linking for budget allocation
- Financial overview and reporting
- Code categorization and search
- Papua New Guinea Kina currency formatting

**Components**:
- **Budget Books**: Annual financial frameworks
- **Budget Codes**: Specific revenue/expenditure items
- **Group Linking**: Connect codes to organizational units

### 6. Legislated Activities Management
**Purpose**: Manage legislation and mandatory activities that must be linked to workplans

**Features**:
- Legislation document management
- Mandatory activity creation and assignment
- Group linking for activities
- Priority and frequency management
- Compliance tracking and reporting

**Navigation Structure**: `Legislation → Activities`

**Components**:
- **Legislation**: Legal documents that define mandatory requirements
- **Activities**: Specific mandatory tasks derived from legislation
- **Group Linking**: Assignment of activities to organizational groups

**Key Features**:
- Pre-listed activities by administrators
- Multiple group assignment per activity
- Priority levels (Critical, High, Medium)
- Frequency settings (Annual, Quarterly, Monthly, etc.)
- Due period specifications
- Compliance notes and requirements

## Technical Implementation

### Design Patterns
- **MVC Architecture**: Clean separation of concerns
- **RESTful API**: Standard HTTP methods for CRUD operations
- **Repository Pattern**: Dummy data methods simulate database operations
- **Component-Based UI**: Reusable card and button components

### UI/UX Features
- **Mobile App-like Interface**: Button-driven navigation
- **Responsive Design**: Bootstrap 5 grid system
- **Consistent Styling**: Maroon and green color scheme
- **Interactive Elements**: Hover effects and transitions
- **Breadcrumb Navigation**: Clear navigation hierarchy
- **Search and Filter**: Real-time data filtering
- **Modal Dialogs**: Confirmation and form dialogs

### Data Management
- **Dummy Data**: Comprehensive mock data for all modules
- **Validation**: Client-side and server-side validation
- **Error Handling**: User-friendly error messages
- **Success Feedback**: Confirmation messages and redirects

## Currency Implementation
All financial displays use Papua New Guinea Kina (K) formatting:
- Budget amounts: "K 2,500,000"
- Revenue/expenditure: "K 1,500,000"
- Claims and allocations: "K 12,450"

## Security Considerations
- **Role-Based Access**: Administrator role required
- **CSRF Protection**: Built-in CodeIgniter 4 CSRF tokens
- **Input Validation**: Server-side validation for all forms
- **File Upload Security**: Image validation and size limits

## Testing URLs
- Main Dashboard: `http://localhost/ipms_two/admin`
- Users Management: `http://localhost/ipms_two/admin/users`
- Create User: `http://localhost/ipms_two/admin/users/create`
- Edit User: `http://localhost/ipms_two/admin/users/1/edit`
- Structures: `http://localhost/ipms_two/admin/structures`
- Appointments: `http://localhost/ipms_two/admin/appointments`
- Plans: `http://localhost/ipms_two/admin/plans`
- Budget Books: `http://localhost/ipms_two/admin/budget-books`
- Legislated Activities: `http://localhost/ipms_two/admin/legislated-activities`
- Legislation: `http://localhost/ipms_two/admin/legislation`
- Create Legislation: `http://localhost/ipms_two/admin/legislation/create`
- Activities: `http://localhost/ipms_two/admin/legislation/1/activities`
- Create Activity: `http://localhost/ipms_two/admin/legislation/1/activities/create`

## Integration Points
- **Dashboard Integration**: Administrator Settings button in main dashboard
- **Role-Based Display**: Shows only for administrator role users
- **Navigation Flow**: Consistent breadcrumb navigation
- **Template Inheritance**: Extends `admin_template.php` for consistency

## Future Enhancements
1. **Database Integration**: Replace dummy data with actual database operations
2. **File Management**: Implement actual file upload and storage
3. **Advanced Search**: Full-text search across all modules
4. **Audit Trail**: Track all administrative changes
5. **Bulk Operations**: Import/export functionality
6. **Reporting**: Generate administrative reports
7. **Notifications**: Real-time notifications for administrative actions

## Code Quality
- **PSR-4 Autoloading**: Proper namespace usage
- **Documentation**: Comprehensive inline documentation
- **Error Handling**: Graceful error handling throughout
- **Performance**: Optimized queries and caching ready
- **Maintainability**: Clean, readable code structure

This Administrator Settings module provides a solid foundation for comprehensive system administration while maintaining the mobile app-like user experience specified in the UI design requirements.

## Recent Updates & Fixes

### Image Path Corrections
- **Fixed user photo references**: Updated all user image paths from `public/assets/images/default-user.png` to `public/assets/system_images/no-users-image.png`
- **Fallback images**: Implemented proper fallback to existing system images:
  - User photos: `public/assets/system_images/no-users-image.png`
  - General images: `public/assets/system_images/no-image.jpg`

### Navigation Enhancements
- **Back navigation buttons**: Added consistent back navigation throughout the admin interface:
  - Main admin dashboard: "Back to Dashboard" button
  - All sub-modules: "Back to Admin" button in page headers
  - User forms: "Back to Users" button
  - Consistent button styling with left arrow icons

### Navigation Structure
```
Dashboard → Administrator Settings → Sub-modules → Forms
    ↑              ↑                    ↑          ↑
    |              |                    |          |
Back to Dashboard  |              Back to Admin   |
                   |                              |
              Back to Dashboard            Back to Users/etc
```

### UI/UX Improvements
- **Consistent button grouping**: Back buttons integrated with existing action buttons
- **Visual hierarchy**: Clear navigation path with breadcrumbs and back buttons
- **Responsive design**: Back buttons work on all screen sizes
- **Icon consistency**: All back buttons use `fas fa-arrow-left` icon

### Error Resolution
- **404 Image Errors**: Resolved all missing image file errors
- **Broken fallbacks**: Fixed onerror handlers to use correct system image paths
- **Navigation flow**: Improved user experience with clear navigation paths
