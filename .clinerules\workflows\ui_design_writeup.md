# IPMS (Integrated Progress Monitoring System) UI Design Write-Up

The Integrated Progress Monitoring System (IPMS) is designed to streamline the monitoring of human resources, organizational plans, budgets, and asset usage, all linked to workplans and Annual Activity Plans (AAPs). The UI design prioritizes a mobile app-like experience, using a button-driven interface without traditional sidebar or top navigation menus. Built with CodeIgniter 4, Bootstrap 5, and MySQL, the system adopts a RESTful approach, with a mix of standard CodeIgniter CRUD operations and Ajax submissions for dynamic interactions. This write-up outlines the UI design, navigation structure, and page navigation for IPMS, ensuring a user-friendly, intuitive, and efficient experience.

## Design Principles
- **Mobile App-Like Experience**: The interface mimics a mobile app, with buttons as the primary navigation mechanism, displayed within the main content area.
- **Simplicity and Clarity**: Clean layouts, minimalistic design, and clear button labels ensure ease of use for all user roles.
- **Role-Based Access**: The interface adapts dynamically based on user roles (Officer, Group Admin, Supervisor, ARO, Fund Manager, Administrator), showing only relevant buttons.
- **Responsive Design**: Using Bootstrap 5, the UI is fully responsive, ensuring seamless functionality across devices (desktop, tablet, mobile).
- **Consistency**: Standardized button styles, colors, and layouts maintain a cohesive look and feel.
- **Accessibility**: High-contrast colors, clear typography, and intuitive navigation ensure accessibility for all users.

## Navigation Structure
The IPMS navigation structure is button-driven, with all menu options presented as interactive buttons within the main content area. The system dynamically displays buttons based on the user’s role, ensuring a tailored experience. Below is the navigation structure, organized by user role and functionality.

### Main Navigation (Button-Based)
- **Standard Buttons** (Available to all users):
  - **My Workplan Activities**: View and manage assigned workplan activities.
  - **My Profile**: View and edit personal details (full name, email, file number, gender, date of birth, date joined, ID photo, contact details, remarks).
  - **My Acquittals**: Submit and view acquittals for financial claims.
  - **My Reports**: Create and view reports for assigned activities.
- **Group Admin Buttons** (In addition to standard buttons):
  - **Group Settings**: Manage group details, recurrent activities, and positions.
- **Supervisor Buttons** (In addition to standard buttons):
  - **Manage Workplans**: Create, assign, and monitor workplans for the group.
- **ARO Buttons** (In addition to standard buttons):
  - **Financial Claims**: Create and manage financial claims linked to workplan activities.
- **Fund Manager Buttons** (In addition to standard buttons):
  - **Financial Claims**: View and manage financial claims.
  - **Claims Workflow**: Approve or reject pending financial claims.
- **Administrator Buttons** (In addition to standard buttons):
  - **Administrator Settings**: Access user management, structure management, appointments, plans management, and budget book management.

### Navigation Flow
- **Home Page (Dashboard)**: Upon login, users land on the Home page, which displays role-specific buttons in a grid or list layout within the main content area. The Home page serves as the central hub for navigation.
- **Button Navigation**: Clicking a button redirects to the corresponding module (e.g., My Workplan Activities, Financial Claims). Each module page contains relevant actions (e.g., view, create, edit) and a “Back” button to return to the Home page.
- **Dynamic Button Display**: Buttons are conditionally rendered based on the user’s role, ensuring only relevant options are visible.
- **No Sidebar/Top Menu**: All navigation occurs through buttons in the main content area, maintaining a clean, mobile app-like interface.

## Page Navigation and Descriptions
Below is a detailed breakdown of each page/module, its purpose, key features, and navigation flow.

### 1. Home Page
- **Purpose**: Central hub displaying role-specific navigation buttons.
- **Key Features**:
  - Displays buttons in a responsive grid or list layout (using Bootstrap 5).
  - Buttons are styled with clear labels, icons, and role-based visibility (e.g., “My Workplan Activities” for all users, “Administrator Settings” for admins only).
  - Header with system name (IPMS), user name, and logout option.
  - Minimal footer with version number and support contact.
- **Navigation**:
  - Clicking a button navigates to the corresponding module (e.g., My Workplan Activities, Financial Claims).
  - No sidebar or top menu; all navigation is button-driven.

### 2. My Workplan Activities
- **Purpose**: Allows users to view and manage their assigned workplan activities.
- **Key Features**:
  - Displays a table of assigned activities (columns: Activity Name, Type [Project/Recurrent/Legislated], Budget Code [if linked], Assigned Assets [if any], Status, Actions).
  - Actions include View, Edit, and Submit Report.
  - Filter and search options to sort activities by type, status, or budget code.
  - Button to create a new workplan activity (if permitted by role).
- **Navigation**:
  - Access via “My Workplan Activities” button on Home page.
  - “Back” button returns to Home page.
  - Clicking “View” or “Edit” opens an activity details page.
  - Clicking “Submit Report” opens a report submission form.

### 3. My Profile
- **Purpose**: Allows users to view and update their personal information.
- **Key Features**:
  - Displays user details (full name, email, file number, gender, date of birth, date joined, ID photo, contact details, remarks).
  - Editable fields with validation (e.g., email format, date format).
  - Option to upload or update ID photo.
  - Save and Cancel buttons for updates.
- **Navigation**:
  - Access via “My Profile” button on Home page.
  - “Back” button returns to Home page.

### 4. My Acquittals
- **Purpose**: Enables users to submit and view acquittals for financial claims.
- **Key Features**:
  - Table listing submitted acquittals (columns: Claim ID, Workplan Activity, Amount, Status, Submission Date, Actions).
  - Form to submit new acquittals, linking to specific financial claims and workplan activities.
  - Ajax-based form submission for real-time feedback.
  - Downloadable PDF versions of acquittals.
- **Navigation**:
  - Access via “My Acquittals” button on Home page.
  - “Back” button returns to Home page.
  - Clicking “View” opens acquittal details.

### 5. My Reports
- **Purpose**: Allows users to create and view reports for assigned activities.
- **Key Features**:
  - Table listing submitted reports (columns: Activity Name, Report Title, Submission Date, Status, Actions).
  - Form to create new reports, with fields for title, description, and file uploads (if needed).
  - Ajax-based submission for seamless user experience.
- **Navigation**:
  - Access via “My Reports” button on Home page.
  - “Back” button returns to Home page.
  - Clicking “View” opens report details.

### 6. Group Settings (Group Admin Only)
- **Purpose**: Enables Group Admins to manage group details, recurrent activities, and positions.
- **Key Features**:
  - Group details form (name, parent group, description).
  - Table for recurrent activities (columns: Activity Name, Budget Code, Linked Groups, Actions [Edit/Delete]).
  - Table for positions (columns: Position No., Name, Grade, Reporting To, Type [Public Servant/Casual], Fund Manager [Yes/No], Supervisor [Yes/No], Group Admin [Yes/No], Actions).
  - CRUD operations for activities and positions (via Ajax for dynamic updates).
- **Navigation**:
  - Access via “Group Settings” button on Home page (visible only to Group Admins).
  - “Back” button returns to Home page.

### 7. Manage Workplans (Supervisor Only)
- **Purpose**: Allows Supervisors to create, assign, and monitor workplans.
- **Key Features**:
  - Table listing workplans (columns: Workplan Name, Assigned Officer, Status, Actions [View/Edit/Assign]).
  - Form to create new workplans, linking to activities (Project, Recurrent, Legislated).
  - Assign activities to officers (positions, not individuals).
  - View progress of workplan activities (e.g., milestones, completion status).
- **Navigation**:
  - Access via “Manage Workplans” button on Home page (visible only to Supervisors).
  - “Back” button returns to Home page.
  - Clicking “View” or “Edit” opens workplan details.

### 8. Financial Claims (ARO and Fund Manager)
- **Purpose**: Enables AROs to create financial claims and Fund Managers to review/approve claims.
- **Key Features**:
  - **For AROs**:
    - Form to create claims, linking to one or more workplan activities.
    - Budget code selection (amount field enabled only if budget code is linked).
    - Total sum displayed dynamically (via Ajax).
    - Generate FF3, FF4, and Alignment Sheet as PDFs (with version numbers for regenerated forms).
    - Alignment Sheet shows links to organizational plans (Corporate/Development), budget codes, expended amounts, and balance due.
  - **For Fund Managers**:
    - Table listing pending claims (columns: Claim ID, Workplan Activity, Amount, Status, Actions [Approve/Reject]).
    - Approve/Reject actions trigger workflow updates (via Ajax).
  - Downloadable PDFs for FF3, FF4, and Alignment Sheet.
- **Navigation**:
  - Access via “Financial Claims” button on Home page (visible to AROs and Fund Managers).
  - Fund Managers also see “Claims Workflow” button for pending approvals.
  - “Back” button returns to Home page.

### 9. Claims Workflow (Fund Manager Only)
- **Purpose**: Allows Fund Managers to manage pending financial claim approvals.
- **Key Features**:
  - Table listing pending claims (columns: Claim ID, Workplan Activity, Amount, Requested By, Actions [Approve/Reject]).
  - Ajax-based approval/rejection for real-time updates.
  - View claim details, including linked workplan activities and budget codes.
- **Navigation**:
  - Access via “Claims Workflow” button on Home page (visible only to Fund Managers).
  - “Back” button returns to Home page.

### 10. Administrator Settings (Administrator Only)
- **Purpose**: Provides Administrators with tools to manage users, structures, plans, and budgets.
- **Key Features**:
  - **Users Management**:
    - Table listing users (columns: Full Name, Email, File Number, Gender, Date of Birth, Date Joined, Actions [Edit/Delete]).
    - Form to create/edit users, including ID photo upload.
  - **Structure Management**:
    - Create/edit structures (only one active at a time).
    - Manage groups (parent-child hierarchy) and positions (Position No., Name, Grade, Reporting To, Type, Fund Manager, Supervisor, Group Admin).
  - **Appointments**:
    - Assign employees to positions (manual or CSV import).
    - Table listing assignments (columns: Employee, Position, Group, Actions).
  - **Plans Management**:
    - **Corporate Plans**: CRUD for Plans, KRAs, KPIs (link KPIs to groups).
    - **Development Plans**: CRUD for Plans, Programs, Projects (link projects to groups).
  - **Budget Book Management**:
    - CRUD for Budget Books (one per fiscal year).
    - Table for revenue/expenditure codes (columns: No., Code, Item, Amount, Linked Groups, Remarks).
  - All operations use a mix of standard CRUD and Ajax for dynamic updates.
- **Navigation**:
  - Access via “Administrator Settings” button on Home page (visible only to Administrators).
  - Sub-modules (Users, Structure, Appointments, Plans, Budget Book) displayed as buttons within the Administrator Settings page.
  - “Back” button returns to Home page or parent module.

## Additional UI Considerations
- **Button Styling**:
  - Buttons use Bootstrap 5 classes (e.g., `btn-primary`, `btn-secondary`) with icons for visual clarity (e.g., Font Awesome icons).
  - Consistent sizing and spacing for a clean, mobile app-like feel.
  - Role-specific buttons highlighted with distinct colors (e.g., blue for standard, green for admin).
- **Forms and Tables**:
  - Forms use Bootstrap 5 form controls with validation feedback (e.g., green/red borders for valid/invalid inputs).
  - Tables are responsive, with pagination, sorting, and filtering (using Bootstrap 5 and DataTables for Ajax-driven tables).
  - Ajax submissions for dynamic updates (e.g., claim approvals, activity creation).
- **PDF Generation**:
  - FF3, FF4, and Alignment Sheet PDFs are generated automatically and downloadable.
  - PDFs include version numbers for regenerated forms.
  - Alignment Sheet visually maps workplan activities to organizational plans and budgets.
- **Error Handling**:
  - User-friendly error messages for invalid inputs or failed submissions.
  - Loading spinners for Ajax operations to indicate progress.
- **Security**:
  - Role-based access ensures users only see relevant buttons and data.
  - CSRF protection for all forms (standard in CodeIgniter 4).
- **Development Environment**:
  - Built on XAMPP server with MySQL backend.
  - RESTful API endpoints for dynamic interactions (e.g., claim approvals, activity updates).

## User Experience Flow Example
1. **Officer Login**:
   - Lands on Home page, sees buttons: My Workplan Activities, My Profile, My Acquittals, My Reports.
   - Clicks “My Workplan Activities” to view assigned tasks, creates a report, or links an activity to a budget code.
2. **ARO Creating a Claim**:
   - From Home page, clicks “Financial Claims.”
   - Fills out a claim form, selects workplan activities, and enters amounts (if budget-linked).
   - Submits form (via Ajax), generates FF3, FF4, and Alignment Sheet PDFs.
   - Returns to Home page via “Back” button.
3. **Fund Manager Approving a Claim**:
   - From Home page, clicks “Claims Workflow.”
   - Views pending claims, approves/rejects via Ajax buttons.
   - Returns to Home page.
4. **Administrator Managing Structure**:
   - From Home page, clicks “Administrator Settings,” then “Structure Management.”
   - Creates a new structure, adds groups and positions, and assigns employees.
   - Returns to Home page via “Back” button.


# IPMS (Integrated Progress Monitoring System) UI Design Write-Up

## Design Principles
- **Mobile App-Like Experience**: Button-driven interface mimicking a mobile app, with no sidebar or top navigation menus.
- **Simplicity and Clarity**: Clean, minimalistic design with clear button labels and intuitive navigation.
- **Role-Based Access**: Dynamic button display based on user roles (Officer, Group Admin, Supervisor, ARO, Fund Manager, Administrator).
- **Responsive Design**: Built with Bootstrap 5 for seamless functionality across devices.
- **Consistency**: Standardized button styles, colors, and layouts.
- **Accessibility**: High-contrast colors and clear typography.

## Navigation Structure
- **Main Navigation (Button-Based)**:
  - **Standard Buttons**: My Workplan Activities, My Profile, My Acquittals, My Reports.
  - **Group Admin**: Group Settings + standard buttons.
  - **Supervisor**: Manage Workplans + standard buttons.
  - **ARO**: Financial Claims + standard buttons.
  - **Fund Manager**: Financial Claims, Claims Workflow + standard buttons.
  - **Administrator**: Administrator Settings + standard buttons.
- **Navigation Flow**:
  - Home page displays role-specific buttons in the main content area.
  - Clicking a button navigates to the corresponding module.
  - “Back” button returns to Home page.
  - No sidebar/top menu; all navigation via buttons.

## Page Navigation and Descriptions
### 1. Home Page
- **Purpose**: Central hub with role-specific navigation buttons.
- **Key Features**: Button grid/list, header with system name and logout, minimal footer.
- **Navigation**: Buttons lead to modules; no sidebar/top menu.

### 2. My Workplan Activities
- **Purpose**: View and manage assigned workplan activities.
- **Key Features**: Table of activities, filter/search, create/edit actions.
- **Navigation**: Access via Home page button; “Back” to Home.

### 3. My Profile
- **Purpose**: View/edit personal details.
- **Key Features**: Editable form, ID photo upload, validation.
- **Navigation**: Access via Home page button; “Back” to Home.

### 4. My Acquittals
- **Purpose**: Submit/view acquittals for financial claims.
- **Key Features**: Table of acquittals, Ajax form submission, PDF downloads.
- **Navigation**: Access via Home page button; “Back” to Home.

### 5. My Reports
- **Purpose**: Create/view reports for activities.
- **Key Features**: Table of reports, Ajax form submission.
- **Navigation**: Access via Home page button; “Back” to Home.

### 6. Group Settings (Group Admin Only)
- **Purpose**: Manage group details, recurrent activities, positions.
- **Key Features**: CRUD for groups, activities, positions (Ajax-based).
- **Navigation**: Access via Home page button; “Back” to Home.

### 7. Manage Workplans (Supervisor Only)
- **Purpose**: Create/assign/monitor workplans.
- **Key Features**: Workplan table, assign activities to positions, progress tracking.
- **Navigation**: Access via Home page button; “Back” to Home.

### 8. Financial Claims (ARO and Fund Manager)
- **Purpose**: Create (ARO) and review/approve (Fund Manager) claims.
- **Key Features**: Claim form, budget code linking, PDF generation (FF3, FF4, Alignment Sheet), Ajax approvals.
- **Navigation**: Access via Home page button; “Back” to Home.

### 9. Claims Workflow (Fund Manager Only)
- **Purpose**: Manage pending claim approvals.
- **Key Features**: Table of pending claims, Ajax approve/reject.
- **Navigation**: Access via Home page button; “Back” to Home.

### 10. Administrator Settings (Administrator Only)
- **Purpose**: Manage users, structures, plans, budgets.
- **Key Features**:
  - Users: CRUD, photo upload.
  - Structure: CRUD for structures, groups, positions.
  - Appointments: Assign employees (manual/CSV).
  - Plans: Corporate (KRAs/KPIs), Development (Programs/Projects).
  - Budget Book: CRUD for fiscal year, revenue/expenditure codes.
- **Navigation**: Access via Home page button; sub-module buttons; “Back” to Home.

## Additional UI Considerations
- **Button Styling**: Bootstrap 5 buttons with icons, role-specific colors.
- **Forms and Tables**: Responsive, validated forms; sortable/filterable tables.
- **PDF Generation**: Auto-generated FF3, FF4, Alignment Sheet with version numbers.
- **Error Handling**: User-friendly messages, Ajax loading spinners.
- **Security**: Role-based access, CSRF protection.
- **Development Environment**: XAMPP, MySQL, CodeIgniter 4, RESTful API.

## User Experience Flow Example
1. **Officer**: Logs in, sees standard buttons, manages activities/reports.
2. **ARO**: Creates claim, links activities, generates PDFs.
3. **Fund Manager**: Approves/rejects claims via workflow.
4. **Administrator**: Manages users, structures, plans, budgets.
