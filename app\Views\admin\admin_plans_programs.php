<?= $this->extend('templates/admin_template') ?>

<?= $this->section('title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('page_title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
    .admin-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .breadcrumb-item a {
        color: #800000;
        text-decoration: none;
    }

    .breadcrumb-item a:hover {
        color: #28a745;
    }

    .table th {
        background-color: #f8f9fa;
        border-top: none;
        color: #800000;
        font-weight: 600;
    }

    .btn-action {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
        margin: 0 0.125rem;
    }

    .status-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }

    .budget-display {
        font-family: 'Courier New', monospace;
        font-weight: bold;
    }

    .progress-bar-container {
        background-color: #333;
        border-radius: 4px;
        height: 8px;
        overflow: hidden;
    }

    .progress-bar-fill {
        height: 100%;
        background: linear-gradient(90deg, #800000, #28a745);
        transition: width 0.3s ease;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="fade-in">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <?php foreach ($breadcrumbs as $index => $breadcrumb): ?>
                        <?php if ($index === count($breadcrumbs) - 1): ?>
                            <li class="breadcrumb-item active" aria-current="page"><?= esc($breadcrumb['title']) ?></li>
                        <?php else: ?>
                            <li class="breadcrumb-item">
                                <a href="<?= esc($breadcrumb['url']) ?>"><?= esc($breadcrumb['title']) ?></a>
                            </li>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="admin-card p-4">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h3 class="mb-2 text-success">
                            <i class="fas fa-project-diagram me-2"></i>Programs Management
                        </h3>
                        <p class="text-muted mb-1">
                            Plan: <strong class="text-primary"><?= esc($plan['name']) ?></strong>
                        </p>
                        <p class="text-muted mb-0 small">
                            Manage programs within this development plan.
                        </p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <div class="btn-group">
                            <a href="<?= site_url('admin/plans') ?>" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to Plans
                            </a>
                            <a href="<?= site_url('admin/plans/' . $plan['id'] . '/programs/create') ?>" class="btn btn-success">
                                <i class="fas fa-plus me-2"></i>Create Program
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Plan Information -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="admin-card p-3">
                <div class="row">
                    <div class="col-md-3">
                        <small class="text-muted">Plan Type</small>
                        <div><span class="badge bg-primary"><?= esc($plan['type']) ?></span></div>
                    </div>
                    <div class="col-md-3">
                        <small class="text-muted">Fiscal Year</small>
                        <div><span class="badge bg-secondary"><?= $plan['fiscal_year'] ?></span></div>
                    </div>
                    <div class="col-md-3">
                        <small class="text-muted">Status</small>
                        <div><span class="badge bg-<?= $plan['status'] === 'Active' ? 'success' : 'secondary' ?>"><?= esc($plan['status']) ?></span></div>
                    </div>
                    <div class="col-md-3">
                        <small class="text-muted">Programs</small>
                        <div><span class="badge bg-info"><?= count($programs) ?></span></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <?php if (session()->getFlashdata('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?= session()->getFlashdata('success') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Programs List -->
    <div class="row">
        <div class="col-12">
            <div class="admin-card p-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h5 class="fw-bold mb-0" style="color: #800000;">
                        <i class="fas fa-cogs me-2"></i>Development Programs
                    </h5>
                    <span class="badge bg-info">
                        <?= count($programs) ?> Program<?= count($programs) !== 1 ? 's' : '' ?>
                    </span>
                </div>

                <?php if (empty($programs)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-project-diagram fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No Programs Found</h5>
                        <p class="text-muted">Create your first program within this development plan.</p>
                        <a href="<?= site_url('admin/plans/' . $plan['id'] . '/programs/create') ?>" class="btn btn-success">
                            <i class="fas fa-plus me-2"></i>Create First Program
                        </a>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Program Name</th>
                                    <th>Budget (PGK)</th>
                                    <th>Duration</th>
                                    <th>Status</th>
                                    <th>Projects</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($programs as $program): ?>
                                <tr>
                                    <td>
                                        <div>
                                            <strong><?= esc($program['name']) ?></strong>
                                            <br>
                                            <small class="text-muted"><?= esc($program['description']) ?></small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="budget-display text-success">
                                            K <?= number_format($program['budget'] / 1000000, 1) ?>M
                                        </span>
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            <?= date('M Y', strtotime($program['start_date'])) ?> -
                                            <?= date('M Y', strtotime($program['end_date'])) ?>
                                        </small>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?= $program['status'] === 'Active' ? 'success' : 'secondary' ?> status-badge">
                                            <?= esc($program['status']) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <a href="<?= site_url('admin/programs/' . $program['id'] . '/projects') ?>"
                                           class="btn btn-outline-info btn-sm">
                                            <i class="fas fa-tasks me-1"></i>View Projects
                                        </a>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="<?= site_url('admin/plans/' . $plan['id'] . '/programs/' . $program['id'] . '/edit') ?>"
                                               class="btn btn-outline-primary btn-action" title="Edit Program">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <form method="POST" action="<?= site_url('admin/plans/' . $plan['id'] . '/programs/' . $program['id']) ?>" class="d-inline">
                                                <?= csrf_field() ?>
                                                <input type="hidden" name="_method" value="DELETE">
                                                <button type="submit" class="btn btn-outline-danger btn-action"
                                                        title="Delete Program"
                                                        onclick="return confirm('Are you sure you want to delete this program? This will also delete all associated projects.')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Navigation Help -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="admin-card p-3">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h6 class="mb-1 text-primary">
                            <i class="fas fa-info-circle me-2"></i>Navigation Flow
                        </h6>
                        <p class="mb-0 text-muted small">
                            Plans → <strong>Programs</strong> → Projects. Click "View Projects" to manage projects within each program.
                        </p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <div class="btn-group">
                            <a href="<?= site_url('admin/plans') ?>" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-arrow-left me-2"></i>Back to Plans
                            </a>
                            <a href="<?= site_url('admin/plans/' . $plan['id'] . '/programs/create') ?>" class="btn btn-success btn-sm">
                                <i class="fas fa-plus me-2"></i>Add Program
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-hide alerts after 5 seconds
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(function(alert) {
            setTimeout(function() {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }, 5000);
        });

        console.log('Programs Management initialized successfully');
    });
</script>
<?= $this->endSection() ?>
