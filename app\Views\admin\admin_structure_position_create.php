<?= $this->extend('templates/admin_template') ?>

<?= $this->section('title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('page_title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
    .admin-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .breadcrumb-item a {
        color: #800000;
        text-decoration: none;
    }

    .breadcrumb-item a:hover {
        color: #28a745;
    }

    .form-label {
        color: #800000;
        font-weight: 600;
    }

    .form-control:focus, .form-select:focus {
        border-color: #28a745;
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
    }

    .btn-success {
        background-color: #28a745;
        border-color: #28a745;
    }

    .btn-success:hover {
        background-color: #218838;
        border-color: #1e7e34;
    }

    .role-section {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .form-check-input:checked {
        background-color: #28a745;
        border-color: #28a745;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="fade-in">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <?php foreach ($breadcrumbs as $index => $breadcrumb): ?>
                        <?php if ($index === count($breadcrumbs) - 1): ?>
                            <li class="breadcrumb-item active" aria-current="page"><?= esc($breadcrumb['title']) ?></li>
                        <?php else: ?>
                            <li class="breadcrumb-item">
                                <a href="<?= esc($breadcrumb['url']) ?>"><?= esc($breadcrumb['title']) ?></a>
                            </li>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="admin-card p-4">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h3 class="mb-2 text-success">
                            <i class="fas fa-plus-circle me-2"></i>Create New Position
                        </h3>
                        <p class="text-muted mb-1">
                            Structure: <strong><?= esc($structure['name']) ?></strong>
                        </p>
                        <p class="text-muted mb-0">
                            Group: <strong><?= esc($group['name']) ?></strong>
                        </p>
                        <p class="text-muted mb-0 small">
                            Create a new position within this group.
                        </p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <a href="<?= site_url('admin/groups/' . $group['id'] . '/positions') ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Back to Positions
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Create Form -->
    <div class="row">
        <div class="col-lg-10 mx-auto">
            <div class="admin-card p-4">
                <form method="POST" action="<?= site_url('admin/groups/' . $group['id'] . '/positions') ?>" id="createPositionForm">
                    <?= csrf_field() ?>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-4">
                                <label for="position_no" class="form-label">
                                    <i class="fas fa-hashtag me-2"></i>Position Number *
                                </label>
                                <input type="text" 
                                       class="form-control" 
                                       id="position_no" 
                                       name="position_no" 
                                       placeholder="e.g., DOT-001, PM-002"
                                       required
                                       maxlength="50">
                                <div class="form-text">
                                    Unique identifier for this position.
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-4">
                                <label for="position_name" class="form-label">
                                    <i class="fas fa-user-tie me-2"></i>Position Name *
                                </label>
                                <input type="text" 
                                       class="form-control" 
                                       id="position_name" 
                                       name="position_name" 
                                       placeholder="e.g., Director, Manager, Analyst"
                                       required
                                       maxlength="255">
                                <div class="form-text">
                                    Descriptive title for this position.
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-4">
                                <label for="grade" class="form-label">
                                    <i class="fas fa-star me-2"></i>Grade
                                </label>
                                <input type="text" 
                                       class="form-control" 
                                       id="grade" 
                                       name="grade" 
                                       placeholder="e.g., ES-10, ES-8, Grade 5"
                                       maxlength="50">
                                <div class="form-text">
                                    Position grade or classification level.
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-4">
                                <label for="position_type" class="form-label">
                                    <i class="fas fa-briefcase me-2"></i>Position Type *
                                </label>
                                <select class="form-select" id="position_type" name="position_type" required>
                                    <option value="">Select Position Type</option>
                                    <option value="Public Servant">Public Servant</option>
                                    <option value="Casual">Casual</option>
                                </select>
                                <div class="form-text">
                                    Employment classification for this position.
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-4">
                        <label for="reporting_to_id" class="form-label">
                            <i class="fas fa-user-check me-2"></i>Reports To
                        </label>
                        <select class="form-select" id="reporting_to_id" name="reporting_to_id">
                            <option value="">Select Reporting Position (Optional)</option>
                            <?php foreach ($reporting_positions as $reportingPosition): ?>
                                <option value="<?= $reportingPosition['id'] ?>">
                                    <?= esc($reportingPosition['position_name']) ?> (<?= esc($reportingPosition['position_no']) ?>)
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <div class="form-text">
                            Select the position this role reports to in the hierarchy.
                        </div>
                    </div>

                    <!-- Roles and Permissions Section -->
                    <div class="role-section">
                        <h6 class="mb-3 text-primary">
                            <i class="fas fa-shield-alt me-2"></i>Roles and Permissions
                        </h6>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-check mb-3">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           id="is_fund_manager" 
                                           name="is_fund_manager" 
                                           value="1">
                                    <label class="form-check-label" for="is_fund_manager">
                                        <strong>Fund Manager</strong>
                                        <br><small class="text-muted">Can manage budgets and financial resources</small>
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check mb-3">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           id="is_supervisor" 
                                           name="is_supervisor" 
                                           value="1">
                                    <label class="form-check-label" for="is_supervisor">
                                        <strong>Supervisor</strong>
                                        <br><small class="text-muted">Can supervise other positions and approve work</small>
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check mb-3">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           id="is_group_admin" 
                                           name="is_group_admin" 
                                           value="1">
                                    <label class="form-check-label" for="is_group_admin">
                                        <strong>Group Admin</strong>
                                        <br><small class="text-muted">Can administer group settings and members</small>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-4">
                        <div class="alert alert-info">
                            <h6 class="alert-heading">
                                <i class="fas fa-info-circle me-2"></i>Position Guidelines
                            </h6>
                            <ul class="mb-0">
                                <li>Position numbers should be unique across the entire system</li>
                                <li>Consider the reporting hierarchy when assigning supervisory roles</li>
                                <li>Fund managers typically need supervisor permissions as well</li>
                                <li>Group admins can manage other positions within their group</li>
                            </ul>
                        </div>
                    </div>

                    <div class="d-flex gap-3">
                        <button type="submit" class="btn btn-success btn-lg">
                            <i class="fas fa-save me-2"></i>Create Position
                        </button>
                        <a href="<?= site_url('admin/groups/' . $group['id'] . '/positions') ?>" class="btn btn-outline-secondary btn-lg">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.getElementById('createPositionForm');
        const positionNoInput = document.getElementById('position_no');
        const positionNameInput = document.getElementById('position_name');
        const fundManagerCheck = document.getElementById('is_fund_manager');
        const supervisorCheck = document.getElementById('is_supervisor');
        
        // Form validation
        form.addEventListener('submit', function(e) {
            const positionNo = positionNoInput.value.trim();
            const positionName = positionNameInput.value.trim();
            
            if (positionNo.length < 2) {
                e.preventDefault();
                alert('Position number must be at least 2 characters long.');
                positionNoInput.focus();
                return false;
            }
            
            if (positionName.length < 3) {
                e.preventDefault();
                alert('Position name must be at least 3 characters long.');
                positionNameInput.focus();
                return false;
            }
            
            // Show loading state
            const submitBtn = form.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Creating...';
            submitBtn.disabled = true;
        });
        
        // Auto-check supervisor when fund manager is selected
        fundManagerCheck.addEventListener('change', function() {
            if (this.checked) {
                supervisorCheck.checked = true;
            }
        });
        
        // Prevent unchecking supervisor if fund manager is checked
        supervisorCheck.addEventListener('change', function() {
            if (!this.checked && fundManagerCheck.checked) {
                this.checked = true;
                alert('Fund managers must have supervisor permissions.');
            }
        });

        console.log('Create Position form initialized successfully');
    });
</script>
<?= $this->endSection() ?>
