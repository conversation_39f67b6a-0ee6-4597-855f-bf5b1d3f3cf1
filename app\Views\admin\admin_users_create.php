<?= $this->extend('templates/admin_template') ?>

<?= $this->section('title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('page_title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
    .admin-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .breadcrumb-item a {
        color: #800000;
        text-decoration: none;
    }

    .breadcrumb-item a:hover {
        color: #28a745;
    }

    .form-label {
        font-weight: 600;
        color: #495057;
    }

    .required {
        color: #dc3545;
    }

    .photo-upload {
        border: 2px dashed #dee2e6;
        border-radius: 10px;
        padding: 2rem;
        text-align: center;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .photo-upload:hover {
        border-color: #28a745;
        background-color: #f8f9fa;
    }

    .photo-upload.dragover {
        border-color: #28a745;
        background-color: #e8f5e9;
    }

    .photo-preview {
        max-width: 150px;
        max-height: 150px;
        border-radius: 10px;
        margin-top: 1rem;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="fade-in">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <?php foreach ($breadcrumbs as $index => $breadcrumb): ?>
                        <?php if ($index === count($breadcrumbs) - 1): ?>
                            <li class="breadcrumb-item active" aria-current="page"><?= esc($breadcrumb['title']) ?></li>
                        <?php else: ?>
                            <li class="breadcrumb-item">
                                <a href="<?= esc($breadcrumb['url']) ?>"><?= esc($breadcrumb['title']) ?></a>
                            </li>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="admin-card p-4">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h3 class="mb-2 text-success">
                            <i class="fas fa-user-plus me-2"></i>Create New User
                        </h3>
                        <p class="text-muted mb-0">
                            Add a new user to the system. Fill in the required information below.
                        </p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <a href="<?= site_url('admin/users') ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Back to Users
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Create User Form -->
    <form action="<?= site_url('admin/users') ?>" method="POST" enctype="multipart/form-data" id="createUserForm">
        <?= csrf_field() ?>

        <div class="row">
            <!-- Personal Information -->
            <div class="col-lg-8">
                <div class="admin-card p-4 mb-4">
                    <h5 class="fw-bold mb-3" style="color: #800000;">
                        <i class="fas fa-user me-2"></i>Personal Information
                    </h5>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="full_name" class="form-label">
                                Full Name <span class="required">*</span>
                            </label>
                            <input type="text" class="form-control" id="full_name" name="full_name" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">
                                Email Address <span class="required">*</span>
                            </label>
                            <input type="email" class="form-control" id="email" name="email" required>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="file_number" class="form-label">File Number</label>
                            <input type="text" class="form-control" id="file_number" name="file_number" placeholder="e.g., EMP001">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="gender" class="form-label">Gender</label>
                            <select class="form-select" id="gender" name="gender">
                                <option value="">Select Gender</option>
                                <option value="Male">Male</option>
                                <option value="Female">Female</option>
                                <option value="Other">Other</option>
                            </select>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="date_of_birth" class="form-label">Date of Birth</label>
                            <input type="date" class="form-control" id="date_of_birth" name="date_of_birth">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="date_joined" class="form-label">Date Joined</label>
                            <input type="date" class="form-control" id="date_joined" name="date_joined" value="<?= date('Y-m-d') ?>">
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="contact_details" class="form-label">Contact Details</label>
                        <textarea class="form-control" id="contact_details" name="contact_details" rows="3" placeholder="Phone, address, etc."></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="remarks" class="form-label">Remarks</label>
                        <textarea class="form-control" id="remarks" name="remarks" rows="2" placeholder="Additional notes or comments"></textarea>
                    </div>
                </div>

                <!-- Security Information -->
                <div class="admin-card p-4">
                    <h5 class="fw-bold mb-3" style="color: #800000;">
                        <i class="fas fa-lock me-2"></i>Security Information
                    </h5>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="password" class="form-label">
                                Password <span class="required">*</span>
                            </label>
                            <input type="password" class="form-control" id="password" name="password" required>
                            <div class="form-text">Minimum 8 characters with letters and numbers</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="confirm_password" class="form-label">
                                Confirm Password <span class="required">*</span>
                            </label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Photo Upload -->
            <div class="col-lg-4">
                <div class="admin-card p-4">
                    <h5 class="fw-bold mb-3" style="color: #800000;">
                        <i class="fas fa-camera me-2"></i>Profile Photo
                    </h5>

                    <div class="photo-upload" id="photoUpload">
                        <i class="fas fa-cloud-upload-alt fs-2 text-muted mb-2"></i>
                        <p class="text-muted mb-2">Click to upload or drag and drop</p>
                        <small class="text-muted">JPG, PNG or GIF (max 2MB)</small>
                        <input type="file" class="d-none" id="id_photo" name="id_photo" accept="image/*">
                        <img id="photoPreview" class="photo-preview d-none" alt="Photo preview">
                    </div>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="admin-card p-4">
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save me-2"></i>Create User
                        </button>
                        <a href="<?= site_url('admin/users') ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const photoUpload = document.getElementById('photoUpload');
        const photoInput = document.getElementById('id_photo');
        const photoPreview = document.getElementById('photoPreview');

        // Photo upload click handler
        photoUpload.addEventListener('click', function() {
            photoInput.click();
        });

        // Photo input change handler
        photoInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    photoPreview.src = e.target.result;
                    photoPreview.classList.remove('d-none');
                };
                reader.readAsDataURL(file);
            }
        });

        // Drag and drop handlers
        photoUpload.addEventListener('dragover', function(e) {
            e.preventDefault();
            this.classList.add('dragover');
        });

        photoUpload.addEventListener('dragleave', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');
        });

        photoUpload.addEventListener('drop', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                photoInput.files = files;
                photoInput.dispatchEvent(new Event('change'));
            }
        });

        // Form validation
        document.getElementById('createUserForm').addEventListener('submit', function(e) {
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirm_password').value;

            if (password !== confirmPassword) {
                e.preventDefault();
                alert('Passwords do not match!');
                return false;
            }

            if (password.length < 8) {
                e.preventDefault();
                alert('Password must be at least 8 characters long!');
                return false;
            }
        });

        console.log('Create User form initialized successfully');
    });
</script>
<?= $this->endSection() ?>
